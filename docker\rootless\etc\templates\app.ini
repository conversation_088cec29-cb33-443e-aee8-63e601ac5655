APP_NAME = $APP_NAME
RUN_USER = $RUN_USER
RUN_MODE = $RUN_MODE

[repository]
ROOT = $GITEA_WORK_DIR/git/repositories

[repository.local]
LOCAL_COPY_PATH = $GITEA_TEMP/local-repo

[repository.upload]
TEMP_PATH = $GITEA_TEMP/uploads

[server]
APP_DATA_PATH = $GITEA_WORK_DIR
SSH_DOMAIN       = $SSH_DOMAIN
HTTP_PORT        = $HTTP_PORT
ROOT_URL         = $ROOT_URL
DISABLE_SSH      = $DISABLE_SSH
; In rootless gitea container only internal ssh server is supported
START_SSH_SERVER = true
SSH_PORT         = $SSH_PORT
SSH_LISTEN_PORT  = $SSH_LISTEN_PORT
BUILTIN_SSH_SERVER_USER = $RUN_USER
LFS_START_SERVER = $LFS_START_SERVER

[database]
PATH = $GITEA_WORK_DIR/data/gitea.db
DB_TYPE = $DB_TYPE
HOST    = $DB_HOST
NAME    = $DB_NAME
USER    = $DB_USER
PASSWD  = $DB_PASSWD

[session]
PROVIDER_CONFIG = $GITEA_WORK_DIR/data/sessions

[picture]
AVATAR_UPLOAD_PATH = $GITEA_WORK_DIR/data/avatars
REPOSITORY_AVATAR_UPLOAD_PATH = $GITEA_WORK_DIR/data/repo-avatars

[attachment]
PATH = $GITEA_WORK_DIR/data/attachments

[log]
ROOT_PATH = $GITEA_WORK_DIR/data/log

[security]
INSTALL_LOCK = $INSTALL_LOCK
SECRET_KEY   = $SECRET_KEY
REVERSE_PROXY_LIMIT = 1
REVERSE_PROXY_TRUSTED_PROXIES = *

[service]
DISABLE_REGISTRATION = $DISABLE_REGISTRATION
REQUIRE_SIGNIN_VIEW  = $REQUIRE_SIGNIN_VIEW

[lfs]
PATH = $GITEA_WORK_DIR/git/lfs
