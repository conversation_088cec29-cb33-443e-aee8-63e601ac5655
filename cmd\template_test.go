// Copyright 2025 The Gitea Authors. All rights reserved.
// SPDX-License-Identifier: MIT

package cmd

import (
	"context"
	"fmt"
	"os"

	"code.gitea.io/gitea/modules/log"
	"code.gitea.io/gitea/modules/setting"
	"code.gitea.io/gitea/modules/templates"

	"github.com/urfave/cli/v3"
)

// CmdTemplateTest represents the available template test sub-command.
var CmdTemplateTest = &cli.Command{
	Name:        "template-test",
	Usage:       "Test template system initialization and basic rendering",
	Description: "This command tests if the template system is properly initialized and can render basic templates",
	Action:      runTemplateTest,
	Flags: []cli.Flag{
		&cli.BoolFlag{
			Name:  "verbose",
			Usage: "Enable verbose logging",
		},
	},
}

func runTemplateTest(ctx *cli.Context) error {
	if ctx.Bool("verbose") {
		_ = log.DelLogger("console")
		log.NewLogger(1000, "console", "console", `{"level": "trace", "colorize": true, "expression": "", "prefix": "", "flags": "stdflags"}`)
	}

	setting.LoadSettings()

	fmt.Println("Testing template system...")

	// Test 1: Check if HTMLRenderer can be initialized
	fmt.Print("1. Initializing HTML renderer... ")
	renderer := templates.HTMLRenderer()
	if renderer == nil {
		fmt.Println("FAILED - renderer is nil")
		return fmt.Errorf("HTML renderer initialization failed")
	}
	fmt.Println("OK")

	// Test 2: Check if we can look up the 500 template
	fmt.Print("2. Looking up status/500 template... ")
	executor, err := renderer.TemplateLookup("status/500", context.Background())
	if err != nil {
		fmt.Printf("FAILED - %v\n", err)
		return fmt.Errorf("template lookup failed: %v", err)
	}
	if executor == nil {
		fmt.Println("FAILED - executor is nil")
		return fmt.Errorf("template executor is nil")
	}
	fmt.Println("OK")

	// Test 3: Check if we can look up base templates
	fmt.Print("3. Looking up base/head_style template... ")
	executor, err = renderer.TemplateLookup("base/head_style", context.Background())
	if err != nil {
		fmt.Printf("FAILED - %v\n", err)
		return fmt.Errorf("base template lookup failed: %v", err)
	}
	fmt.Println("OK")

	// Test 4: Check if we can look up base/alert template
	fmt.Print("4. Looking up base/alert template... ")
	executor, err = renderer.TemplateLookup("base/alert", context.Background())
	if err != nil {
		fmt.Printf("FAILED - %v\n", err)
		return fmt.Errorf("base alert template lookup failed: %v", err)
	}
	fmt.Println("OK")

	fmt.Println("\nTemplate system test completed successfully!")
	fmt.Println("If you're still seeing template errors, the issue may be:")
	fmt.Println("1. Runtime context/data issues")
	fmt.Println("2. Missing locale initialization")
	fmt.Println("3. Asset file system problems")
	fmt.Println("4. Permission issues with template files")

	return nil
}
