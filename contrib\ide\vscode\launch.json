{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch",
      "type": "go",
      "request": "launch",
      "mode": "debug",
      "buildFlags": "",
      "program": "${workspaceRoot}/main.go",
      "env": {
        "GITEA_WORK_DIR": "${workspaceRoot}",
      },
      "args": ["web"],
      "showLog": true
    },
    {
      "name": "Launch (with SQLite3)",
      "type": "go",
      "request": "launch",
      "mode": "debug",
      "buildFlags": "-tags='sqlite sqlite_unlock_notify'",
      "program": "${workspaceRoot}/main.go",
      "env": {
        "GITEA_WORK_DIR": "${workspaceRoot}",
      },
      "args": ["web"],
      "showLog": true
    }
  ]
}
