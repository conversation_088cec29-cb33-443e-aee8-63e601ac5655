name: compliance

on:
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  files-changed:
    uses: ./.github/workflows/files-changed.yml

  lint-backend:
    if: needs.files-changed.outputs.backend == 'true' || needs.files-changed.outputs.actions == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          check-latest: true
      - run: make deps-backend deps-tools
      - run: make lint-backend
        env:
          TAGS: bindata sqlite sqlite_unlock_notify

  lint-templates:
    if: needs.files-changed.outputs.templates == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: astral-sh/setup-uv@v6
      - run: uv python install 3.12
      - uses: actions/setup-node@v4
        with:
          node-version: 24
          cache: npm
          cache-dependency-path: package-lock.json
      - run: make deps-py
      - run: make deps-frontend
      - run: make lint-templates

  lint-yaml:
    if: needs.files-changed.outputs.yaml == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: astral-sh/setup-uv@v6
      - run: uv python install 3.12
      - run: make deps-py
      - run: make lint-yaml

  lint-swagger:
    if: needs.files-changed.outputs.swagger == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 24
          cache: npm
          cache-dependency-path: package-lock.json
      - run: make deps-frontend
      - run: make lint-swagger

  lint-spell:
    if: needs.files-changed.outputs.backend == 'true' || needs.files-changed.outputs.frontend == 'true' || needs.files-changed.outputs.actions == 'true' || needs.files-changed.outputs.docs == 'true' || needs.files-changed.outputs.templates == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          check-latest: true
      - run: make lint-spell

  lint-go-windows:
    if: needs.files-changed.outputs.backend == 'true' || needs.files-changed.outputs.actions == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          check-latest: true
      - run: make deps-backend deps-tools
      - run: make lint-go-windows lint-go-gitea-vet
        env:
          TAGS: bindata sqlite sqlite_unlock_notify
          GOOS: windows
          GOARCH: amd64

  lint-go-gogit:
    if: needs.files-changed.outputs.backend == 'true' || needs.files-changed.outputs.actions == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          check-latest: true
      - run: make deps-backend deps-tools
      - run: make lint-go
        env:
          TAGS: bindata gogit sqlite sqlite_unlock_notify

  checks-backend:
    if: needs.files-changed.outputs.backend == 'true' || needs.files-changed.outputs.actions == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          check-latest: true
      - run: make deps-backend deps-tools
      - run: make --always-make checks-backend # ensure the "go-licenses" make target runs

  frontend:
    if: needs.files-changed.outputs.frontend == 'true' || needs.files-changed.outputs.actions == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 24
          cache: npm
          cache-dependency-path: package-lock.json
      - run: make deps-frontend
      - run: make lint-frontend
      - run: make checks-frontend
      - run: make test-frontend
      - run: make frontend

  backend:
    if: needs.files-changed.outputs.backend == 'true' || needs.files-changed.outputs.actions == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          check-latest: true
      # no frontend build here as backend should be able to build
      # even without any frontend files
      - run: make deps-backend
      - run: go build -o gitea_no_gcc # test if build succeeds without the sqlite tag
      - name: build-backend-arm64
        run: make backend # test cross compile
        env:
          GOOS: linux
          GOARCH: arm64
          TAGS: bindata gogit
      - name: build-backend-windows
        run: go build -o gitea_windows
        env:
          GOOS: windows
          GOARCH: amd64
          TAGS: bindata gogit
      - name: build-backend-386
        run: go build -o gitea_linux_386 # test if compatible with 32 bit
        env:
          GOOS: linux
          GOARCH: 386

  docs:
    if: needs.files-changed.outputs.docs == 'true' || needs.files-changed.outputs.actions == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 24
          cache: npm
          cache-dependency-path: package-lock.json
      - run: make deps-frontend
      - run: make lint-md

  actions:
    if: needs.files-changed.outputs.actions == 'true' || needs.files-changed.outputs.actions == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          check-latest: true
      - run: make lint-actions
