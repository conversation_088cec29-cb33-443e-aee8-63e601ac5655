name: db-tests

on:
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  files-changed:
    uses: ./.github/workflows/files-changed.yml

  test-pgsql:
    if: needs.files-changed.outputs.backend == 'true' || needs.files-changed.outputs.actions == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    services:
      pgsql:
        image: postgres:14
        env:
          POSTGRES_DB: test
          POSTGRES_PASSWORD: postgres
        ports:
          - "5432:5432"
      ldap:
        image: gitea/test-openldap:latest
        ports:
          - "389:389"
          - "636:636"
      minio:
        # as github actions doesn't support "entrypoint", we need to use a non-official image
        # that has a custom entrypoint set to "minio server /data"
        image: bitnami/minio:2023.8.31
        env:
          MINIO_ROOT_USER: 123456
          MINIO_ROOT_PASSWORD: ********
        ports:
          - "9000:9000"
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          check-latest: true
      - name: Add hosts to /etc/hosts
        run: '[ -e "/.dockerenv" ] || [ -e "/run/.containerenv" ] || echo "127.0.0.1 pgsql ldap minio" | sudo tee -a /etc/hosts'
      - run: make deps-backend
      - run: make backend
        env:
          TAGS: bindata
      - name: run migration tests
        run: make test-pgsql-migration
      - name: run tests
        run: make test-pgsql
        timeout-minutes: 50
        env:
          TAGS: bindata gogit
          RACE_ENABLED: true
          TEST_TAGS: gogit
          TEST_LDAP: 1
          USE_REPO_TEST_DIR: 1

  test-sqlite:
    if: needs.files-changed.outputs.backend == 'true' || needs.files-changed.outputs.actions == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          check-latest: true
      - run: make deps-backend
      - run: make backend
        env:
          TAGS: bindata gogit sqlite sqlite_unlock_notify
      - name: run migration tests
        run: make test-sqlite-migration
      - name: run tests
        run: make test-sqlite
        timeout-minutes: 50
        env:
          TAGS: bindata gogit sqlite sqlite_unlock_notify
          RACE_ENABLED: true
          TEST_TAGS: gogit sqlite sqlite_unlock_notify
          USE_REPO_TEST_DIR: 1

  test-unit:
    if: needs.files-changed.outputs.backend == 'true' || needs.files-changed.outputs.actions == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    services:
      elasticsearch:
        image: elasticsearch:7.5.0
        env:
          discovery.type: single-node
        ports:
          - "9200:9200"
      meilisearch:
        image: getmeili/meilisearch:v1
        env:
          MEILI_ENV: development # disable auth
        ports:
          - "7700:7700"
      redis:
        image: redis
        options: >- # wait until redis has started
          --health-cmd "redis-cli ping"
          --health-interval 5s
          --health-timeout 3s
          --health-retries 10
        ports:
          - 6379:6379
      minio:
        image: bitnami/minio:2021.3.17
        env:
          MINIO_ACCESS_KEY: 123456
          MINIO_SECRET_KEY: ********
        ports:
          - "9000:9000"
      devstoreaccount1.azurite.local: # https://github.com/Azure/Azurite/issues/1583
        image: mcr.microsoft.com/azure-storage/azurite:latest
        ports:
          - 10000:10000
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          check-latest: true
      - name: Add hosts to /etc/hosts
        run: '[ -e "/.dockerenv" ] || [ -e "/run/.containerenv" ] || echo "127.0.0.1 minio devstoreaccount1.azurite.local mysql elasticsearch meilisearch smtpimap" | sudo tee -a /etc/hosts'
      - run: make deps-backend
      - run: make backend
        env:
          TAGS: bindata
      - name: unit-tests
        run: make unit-test-coverage test-check
        env:
          TAGS: bindata
          RACE_ENABLED: true
          GITHUB_READ_TOKEN: ${{ secrets.GITHUB_READ_TOKEN }}
      - name: unit-tests-gogit
        run: make unit-test-coverage test-check
        env:
          TAGS: bindata gogit
          RACE_ENABLED: true
          GITHUB_READ_TOKEN: ${{ secrets.GITHUB_READ_TOKEN }}

  test-mysql:
    if: needs.files-changed.outputs.backend == 'true' || needs.files-changed.outputs.actions == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    services:
      mysql:
        # the bitnami mysql image has more options than the official one, it's easier to customize
        image: bitnami/mysql:8.0
        env:
          ALLOW_EMPTY_PASSWORD: true
          MYSQL_DATABASE: testgitea
        ports:
          - "3306:3306"
        options: >-
          --mount type=tmpfs,destination=/bitnami/mysql/data
      elasticsearch:
        image: elasticsearch:7.5.0
        env:
          discovery.type: single-node
        ports:
          - "9200:9200"
      smtpimap:
        image: tabascoterrier/docker-imap-devel:latest
        ports:
          - "25:25"
          - "143:143"
          - "587:587"
          - "993:993"
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          check-latest: true
      - name: Add hosts to /etc/hosts
        run: '[ -e "/.dockerenv" ] || [ -e "/run/.containerenv" ] || echo "127.0.0.1 mysql elasticsearch smtpimap" | sudo tee -a /etc/hosts'
      - run: make deps-backend
      - run: make backend
        env:
          TAGS: bindata
      - name: run migration tests
        run: make test-mysql-migration
      - name: run tests
        # run: make integration-test-coverage (at the moment, no coverage is really handled)
        run: make test-mysql
        env:
          TAGS: bindata
          RACE_ENABLED: true
          USE_REPO_TEST_DIR: 1
          TEST_INDEXER_CODE_ES_URL: "******************************************"

  test-mssql:
    if: needs.files-changed.outputs.backend == 'true' || needs.files-changed.outputs.actions == 'true'
    needs: files-changed
    runs-on: ubuntu-latest
    services:
      mssql:
        image: mcr.microsoft.com/mssql/server:2019-latest
        env:
          ACCEPT_EULA: Y
          MSSQL_PID: Standard
          SA_PASSWORD: MwantsaSecurePassword1
        ports:
          - "1433:1433"
      devstoreaccount1.azurite.local: # https://github.com/Azure/Azurite/issues/1583
        image: mcr.microsoft.com/azure-storage/azurite:latest
        ports:
          - 10000:10000
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
          check-latest: true
      - name: Add hosts to /etc/hosts
        run: '[ -e "/.dockerenv" ] || [ -e "/run/.containerenv" ] || echo "127.0.0.1 mssql devstoreaccount1.azurite.local" | sudo tee -a /etc/hosts'
      - run: make deps-backend
      - run: make backend
        env:
          TAGS: bindata
      - run: make test-mssql-migration
      - name: run tests
        run: make test-mssql
        timeout-minutes: 50
        env:
          TAGS: bindata
          USE_REPO_TEST_DIR: 1
