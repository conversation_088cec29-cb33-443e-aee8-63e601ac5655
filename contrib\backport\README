`backport`
==========

`backport` is a command to help create backports of PRs. It backports a
provided PR from main on to a released version.

It will create a backport branch, cherry-pick the PR's merge commit, adjust
the commit message and then push this back up to your fork's remote.

The default version will read from `docs/config.yml`. You can override this
using the option `--version`.

The upstream branches will be fetched, using the remote `origin`. This can
be overrided using `--upstream`, and fetching can be avoided using
`--no-fetch`.

By default the branch created will be called `backport-$PR-$VERSION`. You
can override this using the option `--backport-branch`. This branch will
be created from `--release-branch` which is `release/$(VERSION)`
by default and will be pulled from `$(UPSTREAM)`.

The merge-commit as determined by the github API will be used as the SHA to
cherry-pick. You can override this using `--cherry-pick`.

The commit message will be amended to add the `Backport` header.
`--no-amend-message` can be set to stop this from happening.

If cherry-pick is successful the backported branch will be pushed up to your
fork using your remote. These will be determined using `git remote -v`. You
can set your fork name using `--fork-user` and your remote name using
`--remote`. You can avoid pushing using `--no-push`.

If the push is successful, `xdg-open` will be called to open a backport url.
You can stop this using `--no-xdg-open`.

Installation
============

```bash
go install contrib/backport/backport.go
```
