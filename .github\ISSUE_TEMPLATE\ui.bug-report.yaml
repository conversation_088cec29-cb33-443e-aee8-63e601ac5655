name: Web Interface Bug Report
description: Something doesn't look quite as it should?  Report it here!
labels: ["type/bug", "topic/ui"]
body:
  - type: markdown
    attributes:
      value: |
        NOTE: If your issue is a security concern, please send an <NAME_EMAIL> instead of opening a public issue.
  - type: markdown
    attributes:
      value: |
        1. Please speak English, this is the language all maintainers can speak and write.
        2. Please ask questions or configuration/deploy problems on our Discord
           server (https://discord.gg/gitea) or forum (https://forum.gitea.com).
        3. Please take a moment to check that your issue doesn't already exist.
        4. Make sure it's not mentioned in the FAQ (https://docs.gitea.com/help/faq)
        5. Please give all relevant information below for bug reports, because
           incomplete details will be handled as an invalid report.
        6. In particular it's really important to provide pertinent logs. If you are certain that this is a javascript
           error, show us the javascript console. If the error appears to relate to Gite<PERSON> the server you must also give us
           DEBUG level logs. (See https://docs.gitea.com/administration/logging-config#collecting-logs-for-help)
  - type: textarea
    id: description
    attributes:
      label: Description
      description: |
        Please provide a description of your issue here, with a URL if you were able to reproduce the issue (see below)
        If using a proxy or a CDN (e.g. CloudFlare) in front of gitea, please disable the proxy/CDN fully and connect to gitea directly to confirm the issue still persists without those services.
  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots
      description: Please provide at least 1 screenshot showing the issue.
    validations:
      required: true
  - type: input
    id: gitea-ver
    attributes:
      label: Gitea Version
      description: Gitea version (or commit reference) your instance is running
    validations:
      required: true
  - type: dropdown
    id: can-reproduce
    attributes:
      label: Can you reproduce the bug on the Gitea demo site?
      description: |
        If so, please provide a URL in the Description field
        URL of Gitea demo: https://demo.gitea.com
      options:
        - "Yes"
        - "No"
    validations:
      required: true
  - type: input
    id: os-ver
    attributes:
      label: Operating System
      description: The operating system you are using to access Gitea
  - type: input
    id: browser-ver
    attributes:
      label: Browser Version
      description: The browser and version that you are using to access Gitea
    validations:
      required: true
