<?xml version="1.0"?>
<!DOCTYPE service_bundle SYSTEM "/usr/share/lib/xml/dtd/service_bundle.dtd.1">
<service_bundle type="manifest" name="export">
	<service name="gitea" type="service" version="1">
		<create_default_instance enabled="false"/>

		<dependency name="network" grouping="require_all" restart_on="refresh" type="service">
			<service_fmri value="svc:/milestone/network:default"/>
		</dependency>

		<dependency name="filesystem" grouping="require_all" restart_on="refresh" type="service">
			<service_fmri value="svc:/system/filesystem/local"/>
		</dependency>

		<exec_method
		    type="method"
		    name="start"
		    exec="/opt/local/bin/gitea web"
		    timeout_seconds="60">
		  <method_context>
		    <method_credential user="git" group="git" />
		    <method_environment>
		      <envvar name='GITEA_WORK_DIR' value='/opt/local/share/gitea'/>
		      <envvar name='GITEA_CUSTOM' value='/opt/local/etc/gitea'/>
		      <envvar name='HOME' value='/var/db/gitea'/>
		      <envvar name='PATH' value='/opt/local/bin:${PATH}'/>
		      <envvar name='USER' value='git'/>
		    </method_environment>
		  </method_context>
		</exec_method>
		<exec_method type="method" name="stop"	exec=":kill" timeout_seconds="60"/>

		<property_group name="application" type="application"></property_group>
		<property_group name="startd" type="framework">
		  <propval name="duration" type="astring" value="child"/>
		  <propval name="ignore_error" type="astring" value="core,signal"/>
		</property_group>

		<template>
			<common_name>
			  <loctext xml:lang="C">A painless, self-hosted Git service</loctext>
			</common_name>
		</template>

	</service>
</service_bundle>
