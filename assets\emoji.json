[{"emoji": "👍", "aliases": ["+1", "thumbsup"]}, {"emoji": "👎", "aliases": ["-1", "thumbsdown"]}, {"emoji": "💯", "aliases": ["100"]}, {"emoji": "🔢", "aliases": ["1234"]}, {"emoji": "🥇", "aliases": ["1st_place_medal"]}, {"emoji": "🥈", "aliases": ["2nd_place_medal"]}, {"emoji": "🥉", "aliases": ["3rd_place_medal"]}, {"emoji": "🎱", "aliases": ["8ball"]}, {"emoji": "🅰️", "aliases": ["a"]}, {"emoji": "🆎", "aliases": ["ab"]}, {"emoji": "🧮", "aliases": ["abacus"]}, {"emoji": "🔤", "aliases": ["abc"]}, {"emoji": "🔡", "aliases": ["abcd"]}, {"emoji": "🉑", "aliases": ["accept"]}, {"emoji": "🪗", "aliases": ["accordion"]}, {"emoji": "🩹", "aliases": ["adhesive_bandage"]}, {"emoji": "🧑", "aliases": ["adult"]}, {"emoji": "🚡", "aliases": ["aerial_tramway"]}, {"emoji": "🇦🇫", "aliases": ["afghanistan"]}, {"emoji": "✈️", "aliases": ["airplane"]}, {"emoji": "🇦🇽", "aliases": ["aland_islands"]}, {"emoji": "⏰", "aliases": ["alarm_clock"]}, {"emoji": "🇦🇱", "aliases": ["albania"]}, {"emoji": "⚗️", "aliases": ["alembic"]}, {"emoji": "🇩🇿", "aliases": ["algeria"]}, {"emoji": "👽", "aliases": ["alien"]}, {"emoji": "🚑", "aliases": ["ambulance"]}, {"emoji": "🇦🇸", "aliases": ["american_samoa"]}, {"emoji": "🏺", "aliases": ["amphora"]}, {"emoji": "🫀", "aliases": ["anatomical_heart"]}, {"emoji": "⚓", "aliases": ["anchor"]}, {"emoji": "🇦🇩", "aliases": ["andorra"]}, {"emoji": "👼", "aliases": ["angel"]}, {"emoji": "💢", "aliases": ["anger"]}, {"emoji": "🇦🇴", "aliases": ["angola"]}, {"emoji": "😠", "aliases": ["angry"]}, {"emoji": "🇦🇮", "aliases": ["anguilla"]}, {"emoji": "😧", "aliases": ["anguished"]}, {"emoji": "🐜", "aliases": ["ant"]}, {"emoji": "🇦🇶", "aliases": ["antarctica"]}, {"emoji": "🇦🇬", "aliases": ["antigua_barbuda"]}, {"emoji": "🍎", "aliases": ["apple"]}, {"emoji": "♒", "aliases": ["aquarius"]}, {"emoji": "🇦🇷", "aliases": ["argentina"]}, {"emoji": "♈", "aliases": ["aries"]}, {"emoji": "🇦🇲", "aliases": ["armenia"]}, {"emoji": "◀️", "aliases": ["arrow_backward"]}, {"emoji": "⏬", "aliases": ["arrow_double_down"]}, {"emoji": "⏫", "aliases": ["arrow_double_up"]}, {"emoji": "⬇️", "aliases": ["arrow_down"]}, {"emoji": "🔽", "aliases": ["arrow_down_small"]}, {"emoji": "▶️", "aliases": ["arrow_forward"]}, {"emoji": "⤵️", "aliases": ["arrow_heading_down"]}, {"emoji": "⤴️", "aliases": ["arrow_heading_up"]}, {"emoji": "⬅️", "aliases": ["arrow_left"]}, {"emoji": "↙️", "aliases": ["arrow_lower_left"]}, {"emoji": "↘️", "aliases": ["arrow_lower_right"]}, {"emoji": "➡️", "aliases": ["arrow_right"]}, {"emoji": "↪️", "aliases": ["arrow_right_hook"]}, {"emoji": "⬆️", "aliases": ["arrow_up"]}, {"emoji": "↕️", "aliases": ["arrow_up_down"]}, {"emoji": "🔼", "aliases": ["arrow_up_small"]}, {"emoji": "↖️", "aliases": ["arrow_upper_left"]}, {"emoji": "↗️", "aliases": ["arrow_upper_right"]}, {"emoji": "🔃", "aliases": ["arrows_clockwise"]}, {"emoji": "🔄", "aliases": ["arrows_counterclockwise"]}, {"emoji": "🎨", "aliases": ["art"]}, {"emoji": "🚛", "aliases": ["articulated_lorry"]}, {"emoji": "🛰️", "aliases": ["artificial_satellite"]}, {"emoji": "🧑‍🎨", "aliases": ["artist"]}, {"emoji": "🇦🇼", "aliases": ["aruba"]}, {"emoji": "🇦🇨", "aliases": ["ascension_island"]}, {"emoji": "*️⃣", "aliases": ["asterisk"]}, {"emoji": "😲", "aliases": ["astonished"]}, {"emoji": "🧑‍🚀", "aliases": ["astronaut"]}, {"emoji": "👟", "aliases": ["athletic_shoe"]}, {"emoji": "🏧", "aliases": ["atm"]}, {"emoji": "⚛️", "aliases": ["atom_symbol"]}, {"emoji": "🇦🇺", "aliases": ["australia"]}, {"emoji": "🇦🇹", "aliases": ["austria"]}, {"emoji": "🛺", "aliases": ["auto_rickshaw"]}, {"emoji": "🥑", "aliases": ["avocado"]}, {"emoji": "🪓", "aliases": ["axe"]}, {"emoji": "🇦🇿", "aliases": ["azerbaijan"]}, {"emoji": "🅱️", "aliases": ["b"]}, {"emoji": "👶", "aliases": ["baby"]}, {"emoji": "🍼", "aliases": ["baby_bottle"]}, {"emoji": "🐤", "aliases": ["baby_chick"]}, {"emoji": "🚼", "aliases": ["baby_symbol"]}, {"emoji": "🔙", "aliases": ["back"]}, {"emoji": "🥓", "aliases": ["bacon"]}, {"emoji": "🦡", "aliases": ["badger"]}, {"emoji": "🏸", "aliases": ["badminton"]}, {"emoji": "🥯", "aliases": ["bagel"]}, {"emoji": "🛄", "aliases": ["baggage_claim"]}, {"emoji": "🥖", "aliases": ["baguette_bread"]}, {"emoji": "🇧🇸", "aliases": ["bahamas"]}, {"emoji": "🇧🇭", "aliases": ["bahrain"]}, {"emoji": "⚖️", "aliases": ["balance_scale"]}, {"emoji": "👨‍🦲", "aliases": ["bald_man"]}, {"emoji": "👩‍🦲", "aliases": ["bald_woman"]}, {"emoji": "🩰", "aliases": ["ballet_shoes"]}, {"emoji": "🎈", "aliases": ["balloon"]}, {"emoji": "🗳️", "aliases": ["ballot_box"]}, {"emoji": "☑️", "aliases": ["ballot_box_with_check"]}, {"emoji": "🎍", "aliases": ["bamboo"]}, {"emoji": "🍌", "aliases": ["banana"]}, {"emoji": "‼️", "aliases": ["bangbang"]}, {"emoji": "🇧🇩", "aliases": ["bangladesh"]}, {"emoji": "🪕", "aliases": ["banjo"]}, {"emoji": "🏦", "aliases": ["bank"]}, {"emoji": "📊", "aliases": ["bar_chart"]}, {"emoji": "🇧🇧", "aliases": ["barbados"]}, {"emoji": "💈", "aliases": ["barber"]}, {"emoji": "⚾", "aliases": ["baseball"]}, {"emoji": "🧺", "aliases": ["basket"]}, {"emoji": "🏀", "aliases": ["basketball"]}, {"emoji": "🦇", "aliases": ["bat"]}, {"emoji": "🛀", "aliases": ["bath"]}, {"emoji": "🛁", "aliases": ["bathtub"]}, {"emoji": "🔋", "aliases": ["battery"]}, {"emoji": "🏖️", "aliases": ["beach_umbrella"]}, {"emoji": "🫘", "aliases": ["beans"]}, {"emoji": "🐻", "aliases": ["bear"]}, {"emoji": "🧔", "aliases": ["bearded_person"]}, {"emoji": "🦫", "aliases": ["beaver"]}, {"emoji": "🛏️", "aliases": ["bed"]}, {"emoji": "🐝", "aliases": ["bee", "honeybee"]}, {"emoji": "🍺", "aliases": ["beer"]}, {"emoji": "🍻", "aliases": ["beers"]}, {"emoji": "🪲", "aliases": ["beetle"]}, {"emoji": "🔰", "aliases": ["beginner"]}, {"emoji": "🇧🇾", "aliases": ["belarus"]}, {"emoji": "🇧🇪", "aliases": ["belgium"]}, {"emoji": "🇧🇿", "aliases": ["belize"]}, {"emoji": "🔔", "aliases": ["bell"]}, {"emoji": "🫑", "aliases": ["bell_pepper"]}, {"emoji": "🛎️", "aliases": ["bellhop_bell"]}, {"emoji": "🇧🇯", "aliases": ["benin"]}, {"emoji": "🍱", "aliases": ["bento"]}, {"emoji": "🇧🇲", "aliases": ["bermuda"]}, {"emoji": "🧃", "aliases": ["beverage_box"]}, {"emoji": "🇧🇹", "aliases": ["bhu<PERSON>"]}, {"emoji": "🚴", "aliases": ["bicyclist"]}, {"emoji": "🚲", "aliases": ["bike"]}, {"emoji": "🚴‍♂️", "aliases": ["biking_man"]}, {"emoji": "🚴‍♀️", "aliases": ["biking_woman"]}, {"emoji": "👙", "aliases": ["bikini"]}, {"emoji": "🧢", "aliases": ["billed_cap"]}, {"emoji": "☣️", "aliases": ["biohazard"]}, {"emoji": "🐦", "aliases": ["bird"]}, {"emoji": "🎂", "aliases": ["birthday"]}, {"emoji": "🦬", "aliases": ["bison"]}, {"emoji": "🫦", "aliases": ["biting_lip"]}, {"emoji": "🐦‍⬛", "aliases": ["black_bird"]}, {"emoji": "🐈‍⬛", "aliases": ["black_cat"]}, {"emoji": "⚫", "aliases": ["black_circle"]}, {"emoji": "🏴", "aliases": ["black_flag"]}, {"emoji": "🖤", "aliases": ["black_heart"]}, {"emoji": "🃏", "aliases": ["black_joker"]}, {"emoji": "⬛", "aliases": ["black_large_square"]}, {"emoji": "◾", "aliases": ["black_medium_small_square"]}, {"emoji": "◼️", "aliases": ["black_medium_square"]}, {"emoji": "✒️", "aliases": ["black_nib"]}, {"emoji": "▪️", "aliases": ["black_small_square"]}, {"emoji": "🔲", "aliases": ["black_square_button"]}, {"emoji": "👱‍♂️", "aliases": ["blond_haired_man"]}, {"emoji": "👱", "aliases": ["blond_haired_person"]}, {"emoji": "👱‍♀️", "aliases": ["blond_haired_woman", "blonde_woman"]}, {"emoji": "🌼", "aliases": ["blossom"]}, {"emoji": "🐡", "aliases": ["blowfish"]}, {"emoji": "📘", "aliases": ["blue_book"]}, {"emoji": "🚙", "aliases": ["blue_car"]}, {"emoji": "💙", "aliases": ["blue_heart"]}, {"emoji": "🟦", "aliases": ["blue_square"]}, {"emoji": "🫐", "aliases": ["blueberries"]}, {"emoji": "😊", "aliases": ["blush"]}, {"emoji": "🐗", "aliases": ["boar"]}, {"emoji": "⛵", "aliases": ["boat", "sailboat"]}, {"emoji": "🇧🇴", "aliases": ["bolivia"]}, {"emoji": "💣", "aliases": ["bomb"]}, {"emoji": "🦴", "aliases": ["bone"]}, {"emoji": "📖", "aliases": ["book", "open_book"]}, {"emoji": "🔖", "aliases": ["bookmark"]}, {"emoji": "📑", "aliases": ["bookmark_tabs"]}, {"emoji": "📚", "aliases": ["books"]}, {"emoji": "💥", "aliases": ["boom", "collision"]}, {"emoji": "🪃", "aliases": ["boomerang"]}, {"emoji": "👢", "aliases": ["boot"]}, {"emoji": "🇧🇦", "aliases": ["bosnia_herzegovina"]}, {"emoji": "🇧🇼", "aliases": ["botswana"]}, {"emoji": "⛹️‍♂️", "aliases": ["bouncing_ball_man", "basketball_man"]}, {"emoji": "⛹️", "aliases": ["bouncing_ball_person"]}, {"emoji": "⛹️‍♀️", "aliases": ["bouncing_ball_woman", "basketball_woman"]}, {"emoji": "💐", "aliases": ["bouquet"]}, {"emoji": "🇧🇻", "aliases": ["bouvet_island"]}, {"emoji": "🙇", "aliases": ["bow"]}, {"emoji": "🏹", "aliases": ["bow_and_arrow"]}, {"emoji": "🙇‍♂️", "aliases": ["bowing_man"]}, {"emoji": "🙇‍♀️", "aliases": ["bowing_woman"]}, {"emoji": "🥣", "aliases": ["bowl_with_spoon"]}, {"emoji": "🎳", "aliases": ["bowling"]}, {"emoji": "🥊", "aliases": ["boxing_glove"]}, {"emoji": "👦", "aliases": ["boy"]}, {"emoji": "🧠", "aliases": ["brain"]}, {"emoji": "🇧🇷", "aliases": ["brazil"]}, {"emoji": "🍞", "aliases": ["bread"]}, {"emoji": "🤱", "aliases": ["breast_feeding"]}, {"emoji": "🧱", "aliases": ["bricks"]}, {"emoji": "🌉", "aliases": ["bridge_at_night"]}, {"emoji": "💼", "aliases": ["briefcase"]}, {"emoji": "🇮🇴", "aliases": ["british_indian_ocean_territory"]}, {"emoji": "🇻🇬", "aliases": ["british_virgin_islands"]}, {"emoji": "🥦", "aliases": ["broccoli"]}, {"emoji": "💔", "aliases": ["broken_heart"]}, {"emoji": "🧹", "aliases": ["broom"]}, {"emoji": "🟤", "aliases": ["brown_circle"]}, {"emoji": "🤎", "aliases": ["brown_heart"]}, {"emoji": "🟫", "aliases": ["brown_square"]}, {"emoji": "🇧🇳", "aliases": ["brunei"]}, {"emoji": "🧋", "aliases": ["bubble_tea"]}, {"emoji": "🫧", "aliases": ["bubbles"]}, {"emoji": "🪣", "aliases": ["bucket"]}, {"emoji": "🐛", "aliases": ["bug"]}, {"emoji": "🏗️", "aliases": ["building_construction"]}, {"emoji": "💡", "aliases": ["bulb"]}, {"emoji": "🇧🇬", "aliases": ["bulgaria"]}, {"emoji": "🚅", "aliases": ["bullettrain_front"]}, {"emoji": "🚄", "aliases": ["bullettrain_side"]}, {"emoji": "🇧🇫", "aliases": ["burkina_faso"]}, {"emoji": "🌯", "aliases": ["burrito"]}, {"emoji": "🇧🇮", "aliases": ["burundi"]}, {"emoji": "🚌", "aliases": ["bus"]}, {"emoji": "🕴️", "aliases": ["business_suit_levitating"]}, {"emoji": "🚏", "aliases": ["busstop"]}, {"emoji": "👤", "aliases": ["bust_in_silhouette"]}, {"emoji": "👥", "aliases": ["busts_in_silhouette"]}, {"emoji": "🧈", "aliases": ["butter"]}, {"emoji": "🦋", "aliases": ["butterfly"]}, {"emoji": "🌵", "aliases": ["cactus"]}, {"emoji": "🍰", "aliases": ["cake"]}, {"emoji": "📆", "aliases": ["calendar"]}, {"emoji": "🤙", "aliases": ["call_me_hand"]}, {"emoji": "📲", "aliases": ["calling"]}, {"emoji": "🇰🇭", "aliases": ["cambodia"]}, {"emoji": "🐫", "aliases": ["camel"]}, {"emoji": "📷", "aliases": ["camera"]}, {"emoji": "📸", "aliases": ["camera_flash"]}, {"emoji": "🇨🇲", "aliases": ["cameroon"]}, {"emoji": "🏕️", "aliases": ["camping"]}, {"emoji": "🇨🇦", "aliases": ["canada"]}, {"emoji": "🇮🇨", "aliases": ["canary_islands"]}, {"emoji": "♋", "aliases": ["cancer"]}, {"emoji": "🕯️", "aliases": ["candle"]}, {"emoji": "🍬", "aliases": ["candy"]}, {"emoji": "🥫", "aliases": ["canned_food"]}, {"emoji": "🛶", "aliases": ["canoe"]}, {"emoji": "🇨🇻", "aliases": ["cape_verde"]}, {"emoji": "🔠", "aliases": ["capital_abcd"]}, {"emoji": "♑", "aliases": ["capricorn"]}, {"emoji": "🚗", "aliases": ["car", "red_car"]}, {"emoji": "🗃️", "aliases": ["card_file_box"]}, {"emoji": "📇", "aliases": ["card_index"]}, {"emoji": "🗂️", "aliases": ["card_index_dividers"]}, {"emoji": "🇧🇶", "aliases": ["caribbean_netherlands"]}, {"emoji": "🎠", "aliases": ["carousel_horse"]}, {"emoji": "🪚", "aliases": ["carpentry_saw"]}, {"emoji": "🥕", "aliases": ["carrot"]}, {"emoji": "🤸", "aliases": ["cartwheeling"]}, {"emoji": "🐱", "aliases": ["cat"]}, {"emoji": "🐈", "aliases": ["cat2"]}, {"emoji": "🇰🇾", "aliases": ["cayman_islands"]}, {"emoji": "💿", "aliases": ["cd"]}, {"emoji": "🇨🇫", "aliases": ["central_african_republic"]}, {"emoji": "🇪🇦", "aliases": ["ceuta_melilla"]}, {"emoji": "🇹🇩", "aliases": ["chad"]}, {"emoji": "⛓️", "aliases": ["chains"]}, {"emoji": "🪑", "aliases": ["chair"]}, {"emoji": "🍾", "aliases": ["champagne"]}, {"emoji": "💹", "aliases": ["chart"]}, {"emoji": "📉", "aliases": ["chart_with_downwards_trend"]}, {"emoji": "📈", "aliases": ["chart_with_upwards_trend"]}, {"emoji": "🏁", "aliases": ["checkered_flag"]}, {"emoji": "🧀", "aliases": ["cheese"]}, {"emoji": "🍒", "aliases": ["cherries"]}, {"emoji": "🌸", "aliases": ["cherry_blossom"]}, {"emoji": "♟️", "aliases": ["chess_pawn"]}, {"emoji": "🌰", "aliases": ["chestnut"]}, {"emoji": "🐔", "aliases": ["chicken"]}, {"emoji": "🧒", "aliases": ["child"]}, {"emoji": "🚸", "aliases": ["children_crossing"]}, {"emoji": "🇨🇱", "aliases": ["chile"]}, {"emoji": "🐿️", "aliases": ["chipmunk"]}, {"emoji": "🍫", "aliases": ["chocolate_bar"]}, {"emoji": "🥢", "aliases": ["chopsticks"]}, {"emoji": "🇨🇽", "aliases": ["christmas_island"]}, {"emoji": "🎄", "aliases": ["christmas_tree"]}, {"emoji": "⛪", "aliases": ["church"]}, {"emoji": "🎦", "aliases": ["cinema"]}, {"emoji": "🎪", "aliases": ["circus_tent"]}, {"emoji": "🌇", "aliases": ["city_sunrise"]}, {"emoji": "🌆", "aliases": ["city_sunset"]}, {"emoji": "🏙️", "aliases": ["cityscape"]}, {"emoji": "🆑", "aliases": ["cl"]}, {"emoji": "🗜️", "aliases": ["clamp"]}, {"emoji": "👏", "aliases": ["clap"]}, {"emoji": "🎬", "aliases": ["clapper"]}, {"emoji": "🏛️", "aliases": ["classical_building"]}, {"emoji": "🧗", "aliases": ["climbing"]}, {"emoji": "🧗‍♂️", "aliases": ["climbing_man"]}, {"emoji": "🧗‍♀️", "aliases": ["climbing_woman"]}, {"emoji": "🥂", "aliases": ["clinking_glasses"]}, {"emoji": "📋", "aliases": ["clipboard"]}, {"emoji": "🇨🇵", "aliases": ["clipperton_island"]}, {"emoji": "🕐", "aliases": ["clock1"]}, {"emoji": "🕙", "aliases": ["clock10"]}, {"emoji": "🕥", "aliases": ["clock1030"]}, {"emoji": "🕚", "aliases": ["clock11"]}, {"emoji": "🕦", "aliases": ["clock1130"]}, {"emoji": "🕛", "aliases": ["clock12"]}, {"emoji": "🕧", "aliases": ["clock1230"]}, {"emoji": "🕜", "aliases": ["clock130"]}, {"emoji": "🕑", "aliases": ["clock2"]}, {"emoji": "🕝", "aliases": ["clock230"]}, {"emoji": "🕒", "aliases": ["clock3"]}, {"emoji": "🕞", "aliases": ["clock330"]}, {"emoji": "🕓", "aliases": ["clock4"]}, {"emoji": "🕟", "aliases": ["clock430"]}, {"emoji": "🕔", "aliases": ["clock5"]}, {"emoji": "🕠", "aliases": ["clock530"]}, {"emoji": "🕕", "aliases": ["clock6"]}, {"emoji": "🕡", "aliases": ["clock630"]}, {"emoji": "🕖", "aliases": ["clock7"]}, {"emoji": "🕢", "aliases": ["clock730"]}, {"emoji": "🕗", "aliases": ["clock8"]}, {"emoji": "🕣", "aliases": ["clock830"]}, {"emoji": "🕘", "aliases": ["clock9"]}, {"emoji": "🕤", "aliases": ["clock930"]}, {"emoji": "📕", "aliases": ["closed_book"]}, {"emoji": "🔐", "aliases": ["closed_lock_with_key"]}, {"emoji": "🌂", "aliases": ["closed_umbrella"]}, {"emoji": "☁️", "aliases": ["cloud"]}, {"emoji": "🌩️", "aliases": ["cloud_with_lightning"]}, {"emoji": "⛈️", "aliases": ["cloud_with_lightning_and_rain"]}, {"emoji": "🌧️", "aliases": ["cloud_with_rain"]}, {"emoji": "🌨️", "aliases": ["cloud_with_snow"]}, {"emoji": "🤡", "aliases": ["clown_face"]}, {"emoji": "♣️", "aliases": ["clubs"]}, {"emoji": "🇨🇳", "aliases": ["cn"]}, {"emoji": "🧥", "aliases": ["coat"]}, {"emoji": "🪳", "aliases": ["cockroach"]}, {"emoji": "🍸", "aliases": ["cocktail"]}, {"emoji": "🥥", "aliases": ["coconut"]}, {"emoji": "🇨🇨", "aliases": ["cocos_islands"]}, {"emoji": "☕", "aliases": ["coffee"]}, {"emoji": "⚰️", "aliases": ["coffin"]}, {"emoji": "🪙", "aliases": ["coin"]}, {"emoji": "🥶", "aliases": ["cold_face"]}, {"emoji": "😰", "aliases": ["cold_sweat"]}, {"emoji": "🇨🇴", "aliases": ["colombia"]}, {"emoji": "☄️", "aliases": ["comet"]}, {"emoji": "🇰🇲", "aliases": ["comoros"]}, {"emoji": "🧭", "aliases": ["compass"]}, {"emoji": "💻", "aliases": ["computer"]}, {"emoji": "🖱️", "aliases": ["computer_mouse"]}, {"emoji": "🎊", "aliases": ["confetti_ball"]}, {"emoji": "😖", "aliases": ["confounded"]}, {"emoji": "😕", "aliases": ["confused"]}, {"emoji": "🇨🇬", "aliases": ["congo_brazzaville"]}, {"emoji": "🇨🇩", "aliases": ["congo_kinshasa"]}, {"emoji": "㊗️", "aliases": ["congratulations"]}, {"emoji": "🚧", "aliases": ["construction"]}, {"emoji": "👷", "aliases": ["construction_worker"]}, {"emoji": "👷‍♂️", "aliases": ["construction_worker_man"]}, {"emoji": "👷‍♀️", "aliases": ["construction_worker_woman"]}, {"emoji": "🎛️", "aliases": ["control_knobs"]}, {"emoji": "🏪", "aliases": ["convenience_store"]}, {"emoji": "🧑‍🍳", "aliases": ["cook"]}, {"emoji": "🇨🇰", "aliases": ["cook_islands"]}, {"emoji": "🍪", "aliases": ["cookie"]}, {"emoji": "🆒", "aliases": ["cool"]}, {"emoji": "©️", "aliases": ["copyright"]}, {"emoji": "🪸", "aliases": ["coral"]}, {"emoji": "🌽", "aliases": ["corn"]}, {"emoji": "🇨🇷", "aliases": ["costa_rica"]}, {"emoji": "🇨🇮", "aliases": ["cote_divoire"]}, {"emoji": "🛋️", "aliases": ["couch_and_lamp"]}, {"emoji": "👫", "aliases": ["couple"]}, {"emoji": "💑", "aliases": ["couple_with_heart"]}, {"emoji": "👨‍❤️‍👨", "aliases": ["couple_with_heart_man_man"]}, {"emoji": "👩‍❤️‍👨", "aliases": ["couple_with_heart_woman_man"]}, {"emoji": "👩‍❤️‍👩", "aliases": ["couple_with_heart_woman_woman"]}, {"emoji": "💏", "aliases": ["couplekiss"]}, {"emoji": "👨‍❤️‍💋‍👨", "aliases": ["couplekiss_man_man"]}, {"emoji": "👩‍❤️‍💋‍👨", "aliases": ["couplekiss_man_woman"]}, {"emoji": "👩‍❤️‍💋‍👩", "aliases": ["couplekiss_woman_woman"]}, {"emoji": "🐮", "aliases": ["cow"]}, {"emoji": "🐄", "aliases": ["cow2"]}, {"emoji": "🤠", "aliases": ["cowboy_hat_face"]}, {"emoji": "🦀", "aliases": ["crab"]}, {"emoji": "🖍️", "aliases": ["crayon"]}, {"emoji": "💳", "aliases": ["credit_card"]}, {"emoji": "🌙", "aliases": ["crescent_moon"]}, {"emoji": "🦗", "aliases": ["cricket"]}, {"emoji": "🏏", "aliases": ["cricket_game"]}, {"emoji": "🇭🇷", "aliases": ["croatia"]}, {"emoji": "🐊", "aliases": ["crocodile"]}, {"emoji": "🥐", "aliases": ["croissant"]}, {"emoji": "🤞", "aliases": ["crossed_fingers"]}, {"emoji": "🎌", "aliases": ["crossed_flags"]}, {"emoji": "⚔️", "aliases": ["crossed_swords"]}, {"emoji": "👑", "aliases": ["crown"]}, {"emoji": "🩼", "aliases": ["crutch"]}, {"emoji": "😢", "aliases": ["cry"]}, {"emoji": "😿", "aliases": ["crying_cat_face"]}, {"emoji": "🔮", "aliases": ["crystal_ball"]}, {"emoji": "🇨🇺", "aliases": ["cuba"]}, {"emoji": "🥒", "aliases": ["cucumber"]}, {"emoji": "🥤", "aliases": ["cup_with_straw"]}, {"emoji": "🧁", "aliases": ["cupcake"]}, {"emoji": "💘", "aliases": ["cupid"]}, {"emoji": "🇨🇼", "aliases": ["curacao"]}, {"emoji": "🥌", "aliases": ["curling_stone"]}, {"emoji": "👨‍🦱", "aliases": ["curly_haired_man"]}, {"emoji": "👩‍🦱", "aliases": ["curly_haired_woman"]}, {"emoji": "➰", "aliases": ["curly_loop"]}, {"emoji": "💱", "aliases": ["currency_exchange"]}, {"emoji": "🍛", "aliases": ["curry"]}, {"emoji": "🤬", "aliases": ["cursing_face"]}, {"emoji": "🍮", "aliases": ["custard"]}, {"emoji": "🛃", "aliases": ["customs"]}, {"emoji": "🥩", "aliases": ["cut_of_meat"]}, {"emoji": "🌀", "aliases": ["cyclone"]}, {"emoji": "🇨🇾", "aliases": ["cyprus"]}, {"emoji": "🇨🇿", "aliases": ["czech_republic"]}, {"emoji": "🗡️", "aliases": ["dagger"]}, {"emoji": "👯", "aliases": ["dancers"]}, {"emoji": "👯‍♂️", "aliases": ["dancing_men"]}, {"emoji": "👯‍♀️", "aliases": ["dancing_women"]}, {"emoji": "🍡", "aliases": ["dango"]}, {"emoji": "🕶️", "aliases": ["dark_sunglasses"]}, {"emoji": "🎯", "aliases": ["dart"]}, {"emoji": "💨", "aliases": ["dash"]}, {"emoji": "📅", "aliases": ["date"]}, {"emoji": "🇩🇪", "aliases": ["de"]}, {"emoji": "🧏‍♂️", "aliases": ["deaf_man"]}, {"emoji": "🧏", "aliases": ["deaf_person"]}, {"emoji": "🧏‍♀️", "aliases": ["deaf_woman"]}, {"emoji": "🌳", "aliases": ["deciduous_tree"]}, {"emoji": "🦌", "aliases": ["deer"]}, {"emoji": "🇩🇰", "aliases": ["denmark"]}, {"emoji": "🏬", "aliases": ["department_store"]}, {"emoji": "🏚️", "aliases": ["derelict_house"]}, {"emoji": "🏜️", "aliases": ["desert"]}, {"emoji": "🏝️", "aliases": ["desert_island"]}, {"emoji": "🖥️", "aliases": ["desktop_computer"]}, {"emoji": "🕵️", "aliases": ["detective"]}, {"emoji": "💠", "aliases": ["diamond_shape_with_a_dot_inside"]}, {"emoji": "♦️", "aliases": ["diamonds"]}, {"emoji": "🇩🇬", "aliases": ["diego_garcia"]}, {"emoji": "😞", "aliases": ["disappointed"]}, {"emoji": "😥", "aliases": ["disappointed_relieved"]}, {"emoji": "🥸", "aliases": ["disguised_face"]}, {"emoji": "🤿", "aliases": ["diving_mask"]}, {"emoji": "🪔", "aliases": ["diya_lamp"]}, {"emoji": "💫", "aliases": ["dizzy"]}, {"emoji": "😵", "aliases": ["dizzy_face"]}, {"emoji": "🇩🇯", "aliases": ["djibouti"]}, {"emoji": "🧬", "aliases": ["dna"]}, {"emoji": "🚯", "aliases": ["do_not_litter"]}, {"emoji": "🦤", "aliases": ["dodo"]}, {"emoji": "🐶", "aliases": ["dog"]}, {"emoji": "🐕", "aliases": ["dog2"]}, {"emoji": "💵", "aliases": ["dollar"]}, {"emoji": "🎎", "aliases": ["dolls"]}, {"emoji": "🐬", "aliases": ["dolphin", "flipper"]}, {"emoji": "🇩🇲", "aliases": ["dominica"]}, {"emoji": "🇩🇴", "aliases": ["dominican_republic"]}, {"emoji": "🫏", "aliases": ["donkey"]}, {"emoji": "🚪", "aliases": ["door"]}, {"emoji": "🫥", "aliases": ["dotted_line_face"]}, {"emoji": "🍩", "aliases": ["doughnut"]}, {"emoji": "🕊️", "aliases": ["dove"]}, {"emoji": "🐉", "aliases": ["dragon"]}, {"emoji": "🐲", "aliases": ["dragon_face"]}, {"emoji": "👗", "aliases": ["dress"]}, {"emoji": "🐪", "aliases": ["dromedary_camel"]}, {"emoji": "🤤", "aliases": ["drooling_face"]}, {"emoji": "🩸", "aliases": ["drop_of_blood"]}, {"emoji": "💧", "aliases": ["droplet"]}, {"emoji": "🥁", "aliases": ["drum"]}, {"emoji": "🦆", "aliases": ["duck"]}, {"emoji": "🥟", "aliases": ["dumpling"]}, {"emoji": "📀", "aliases": ["dvd"]}, {"emoji": "🦅", "aliases": ["eagle"]}, {"emoji": "👂", "aliases": ["ear"]}, {"emoji": "🌾", "aliases": ["ear_of_rice"]}, {"emoji": "🦻", "aliases": ["ear_with_hearing_aid"]}, {"emoji": "🌍", "aliases": ["earth_africa"]}, {"emoji": "🌎", "aliases": ["earth_americas"]}, {"emoji": "🌏", "aliases": ["earth_asia"]}, {"emoji": "🇪🇨", "aliases": ["ecuador"]}, {"emoji": "🥚", "aliases": ["egg"]}, {"emoji": "🍆", "aliases": ["eggplant"]}, {"emoji": "🇪🇬", "aliases": ["egypt"]}, {"emoji": "8️⃣", "aliases": ["eight"]}, {"emoji": "✴️", "aliases": ["eight_pointed_black_star"]}, {"emoji": "✳️", "aliases": ["eight_spoked_asterisk"]}, {"emoji": "⏏️", "aliases": ["eject_button"]}, {"emoji": "🇸🇻", "aliases": ["el_salvador"]}, {"emoji": "🔌", "aliases": ["electric_plug"]}, {"emoji": "🐘", "aliases": ["elephant"]}, {"emoji": "🛗", "aliases": ["elevator"]}, {"emoji": "🧝", "aliases": ["elf"]}, {"emoji": "🧝‍♂️", "aliases": ["elf_man"]}, {"emoji": "🧝‍♀️", "aliases": ["elf_woman"]}, {"emoji": "📧", "aliases": ["email", "e-mail"]}, {"emoji": "🪹", "aliases": ["empty_nest"]}, {"emoji": "🔚", "aliases": ["end"]}, {"emoji": "🏴󠁧󠁢󠁥󠁮󠁧󠁿", "aliases": ["england"]}, {"emoji": "✉️", "aliases": ["envelope"]}, {"emoji": "📩", "aliases": ["envelope_with_arrow"]}, {"emoji": "🇬🇶", "aliases": ["equatorial_guinea"]}, {"emoji": "🇪🇷", "aliases": ["eritrea"]}, {"emoji": "🇪🇸", "aliases": ["es"]}, {"emoji": "🇪🇪", "aliases": ["estonia"]}, {"emoji": "🇪🇹", "aliases": ["ethiopia"]}, {"emoji": "🇪🇺", "aliases": ["eu", "european_union"]}, {"emoji": "💶", "aliases": ["euro"]}, {"emoji": "🏰", "aliases": ["european_castle"]}, {"emoji": "🏤", "aliases": ["european_post_office"]}, {"emoji": "🌲", "aliases": ["evergreen_tree"]}, {"emoji": "❗", "aliases": ["exclamation", "heavy_exclamation_mark"]}, {"emoji": "🤯", "aliases": ["exploding_head"]}, {"emoji": "😑", "aliases": ["expressionless"]}, {"emoji": "👁️", "aliases": ["eye"]}, {"emoji": "👁️‍🗨️", "aliases": ["eye_speech_bubble"]}, {"emoji": "👓", "aliases": ["eyeglasses"]}, {"emoji": "👀", "aliases": ["eyes"]}, {"emoji": "😮‍💨", "aliases": ["face_exhaling"]}, {"emoji": "🥹", "aliases": ["face_holding_back_tears"]}, {"emoji": "😶‍🌫️", "aliases": ["face_in_clouds"]}, {"emoji": "🫤", "aliases": ["face_with_diagonal_mouth"]}, {"emoji": "🤕", "aliases": ["face_with_head_bandage"]}, {"emoji": "🫢", "aliases": ["face_with_open_eyes_and_hand_over_mouth"]}, {"emoji": "🫣", "aliases": ["face_with_peeking_eye"]}, {"emoji": "😵‍💫", "aliases": ["face_with_spiral_eyes"]}, {"emoji": "🤒", "aliases": ["face_with_thermometer"]}, {"emoji": "🤦", "aliases": ["facepalm"]}, {"emoji": "🏭", "aliases": ["factory"]}, {"emoji": "🧑‍🏭", "aliases": ["factory_worker"]}, {"emoji": "🧚", "aliases": ["fairy"]}, {"emoji": "🧚‍♂️", "aliases": ["fairy_man"]}, {"emoji": "🧚‍♀️", "aliases": ["fairy_woman"]}, {"emoji": "🧆", "aliases": ["falafel"]}, {"emoji": "🇫🇰", "aliases": ["falkland_islands"]}, {"emoji": "🍂", "aliases": ["fallen_leaf"]}, {"emoji": "👪", "aliases": ["family"]}, {"emoji": "👨‍👦", "aliases": ["family_man_boy"]}, {"emoji": "👨‍👦‍👦", "aliases": ["family_man_boy_boy"]}, {"emoji": "👨‍👧", "aliases": ["family_man_girl"]}, {"emoji": "👨‍👧‍👦", "aliases": ["family_man_girl_boy"]}, {"emoji": "👨‍👧‍👧", "aliases": ["family_man_girl_girl"]}, {"emoji": "👨‍👨‍👦", "aliases": ["family_man_man_boy"]}, {"emoji": "👨‍👨‍👦‍👦", "aliases": ["family_man_man_boy_boy"]}, {"emoji": "👨‍👨‍👧", "aliases": ["family_man_man_girl"]}, {"emoji": "👨‍👨‍👧‍👦", "aliases": ["family_man_man_girl_boy"]}, {"emoji": "👨‍👨‍👧‍👧", "aliases": ["family_man_man_girl_girl"]}, {"emoji": "👨‍👩‍👦", "aliases": ["family_man_woman_boy"]}, {"emoji": "👨‍👩‍👦‍👦", "aliases": ["family_man_woman_boy_boy"]}, {"emoji": "👨‍👩‍👧", "aliases": ["family_man_woman_girl"]}, {"emoji": "👨‍👩‍👧‍👦", "aliases": ["family_man_woman_girl_boy"]}, {"emoji": "👨‍👩‍👧‍👧", "aliases": ["family_man_woman_girl_girl"]}, {"emoji": "👩‍👦", "aliases": ["family_woman_boy"]}, {"emoji": "👩‍👦‍👦", "aliases": ["family_woman_boy_boy"]}, {"emoji": "👩‍👧", "aliases": ["family_woman_girl"]}, {"emoji": "👩‍👧‍👦", "aliases": ["family_woman_girl_boy"]}, {"emoji": "👩‍👧‍👧", "aliases": ["family_woman_girl_girl"]}, {"emoji": "👩‍👩‍👦", "aliases": ["family_woman_woman_boy"]}, {"emoji": "👩‍👩‍👦‍👦", "aliases": ["family_woman_woman_boy_boy"]}, {"emoji": "👩‍👩‍👧", "aliases": ["family_woman_woman_girl"]}, {"emoji": "👩‍👩‍👧‍👦", "aliases": ["family_woman_woman_girl_boy"]}, {"emoji": "👩‍👩‍👧‍👧", "aliases": ["family_woman_woman_girl_girl"]}, {"emoji": "🧑‍🌾", "aliases": ["farmer"]}, {"emoji": "🇫🇴", "aliases": ["faroe_islands"]}, {"emoji": "⏩", "aliases": ["fast_forward"]}, {"emoji": "📠", "aliases": ["fax"]}, {"emoji": "😨", "aliases": ["fearful"]}, {"emoji": "🪶", "aliases": ["feather"]}, {"emoji": "🐾", "aliases": ["feet", "paw_prints"]}, {"emoji": "🕵️‍♀️", "aliases": ["female_detective"]}, {"emoji": "♀️", "aliases": ["female_sign"]}, {"emoji": "🎡", "aliases": ["ferris_wheel"]}, {"emoji": "⛴️", "aliases": ["ferry"]}, {"emoji": "🏑", "aliases": ["field_hockey"]}, {"emoji": "🇫🇯", "aliases": ["fiji"]}, {"emoji": "🗄️", "aliases": ["file_cabinet"]}, {"emoji": "📁", "aliases": ["file_folder"]}, {"emoji": "📽️", "aliases": ["film_projector"]}, {"emoji": "🎞️", "aliases": ["film_strip"]}, {"emoji": "🇫🇮", "aliases": ["finland"]}, {"emoji": "🔥", "aliases": ["fire"]}, {"emoji": "🚒", "aliases": ["fire_engine"]}, {"emoji": "🧯", "aliases": ["fire_extinguisher"]}, {"emoji": "🧨", "aliases": ["firecracker"]}, {"emoji": "🧑‍🚒", "aliases": ["firefighter"]}, {"emoji": "🎆", "aliases": ["fireworks"]}, {"emoji": "🌓", "aliases": ["first_quarter_moon"]}, {"emoji": "🌛", "aliases": ["first_quarter_moon_with_face"]}, {"emoji": "🐟", "aliases": ["fish"]}, {"emoji": "🍥", "aliases": ["fish_cake"]}, {"emoji": "🎣", "aliases": ["fishing_pole_and_fish"]}, {"emoji": "🤛", "aliases": ["fist_left"]}, {"emoji": "👊", "aliases": ["fist_oncoming", "facepunch", "punch"]}, {"emoji": "✊", "aliases": ["fist_raised", "fist"]}, {"emoji": "🤜", "aliases": ["fist_right"]}, {"emoji": "5️⃣", "aliases": ["five"]}, {"emoji": "🎏", "aliases": ["flags"]}, {"emoji": "🦩", "aliases": ["flamingo"]}, {"emoji": "🔦", "aliases": ["flashlight"]}, {"emoji": "🥿", "aliases": ["flat_shoe"]}, {"emoji": "🫓", "aliases": ["flatbread"]}, {"emoji": "⚜️", "aliases": ["fleur_de_lis"]}, {"emoji": "🛬", "aliases": ["flight_arrival"]}, {"emoji": "🛫", "aliases": ["flight_departure"]}, {"emoji": "💾", "aliases": ["floppy_disk"]}, {"emoji": "🎴", "aliases": ["flower_playing_cards"]}, {"emoji": "😳", "aliases": ["flushed"]}, {"emoji": "🪈", "aliases": ["flute"]}, {"emoji": "🪰", "aliases": ["fly"]}, {"emoji": "🥏", "aliases": ["flying_disc"]}, {"emoji": "🛸", "aliases": ["flying_saucer"]}, {"emoji": "🌫️", "aliases": ["fog"]}, {"emoji": "🌁", "aliases": ["foggy"]}, {"emoji": "🪭", "aliases": ["folding_hand_fan"]}, {"emoji": "🫕", "aliases": ["fondue"]}, {"emoji": "🦶", "aliases": ["foot"]}, {"emoji": "🏈", "aliases": ["football"]}, {"emoji": "👣", "aliases": ["footprints"]}, {"emoji": "🍴", "aliases": ["fork_and_knife"]}, {"emoji": "🥠", "aliases": ["fortune_cookie"]}, {"emoji": "⛲", "aliases": ["fountain"]}, {"emoji": "🖋️", "aliases": ["fountain_pen"]}, {"emoji": "4️⃣", "aliases": ["four"]}, {"emoji": "🍀", "aliases": ["four_leaf_clover"]}, {"emoji": "🦊", "aliases": ["fox_face"]}, {"emoji": "🇫🇷", "aliases": ["fr"]}, {"emoji": "🖼️", "aliases": ["framed_picture"]}, {"emoji": "🆓", "aliases": ["free"]}, {"emoji": "🇬🇫", "aliases": ["french_guiana"]}, {"emoji": "🇵🇫", "aliases": ["french_polynesia"]}, {"emoji": "🇹🇫", "aliases": ["french_southern_territories"]}, {"emoji": "🍳", "aliases": ["fried_egg"]}, {"emoji": "🍤", "aliases": ["fried_shrimp"]}, {"emoji": "🍟", "aliases": ["fries"]}, {"emoji": "🐸", "aliases": ["frog"]}, {"emoji": "😦", "aliases": ["frowning"]}, {"emoji": "☹️", "aliases": ["frowning_face"]}, {"emoji": "🙍‍♂️", "aliases": ["frowning_man"]}, {"emoji": "🙍", "aliases": ["frowning_person"]}, {"emoji": "🙍‍♀️", "aliases": ["frowning_woman"]}, {"emoji": "⛽", "aliases": ["fuelpump"]}, {"emoji": "🌕", "aliases": ["full_moon"]}, {"emoji": "🌝", "aliases": ["full_moon_with_face"]}, {"emoji": "⚱️", "aliases": ["funeral_urn"]}, {"emoji": "🇬🇦", "aliases": ["gabon"]}, {"emoji": "🇬🇲", "aliases": ["gambia"]}, {"emoji": "🎲", "aliases": ["game_die"]}, {"emoji": "🧄", "aliases": ["garlic"]}, {"emoji": "🇬🇧", "aliases": ["gb", "uk"]}, {"emoji": "⚙️", "aliases": ["gear"]}, {"emoji": "💎", "aliases": ["gem"]}, {"emoji": "♊", "aliases": ["gemini"]}, {"emoji": "🧞", "aliases": ["genie"]}, {"emoji": "🧞‍♂️", "aliases": ["genie_man"]}, {"emoji": "🧞‍♀️", "aliases": ["genie_woman"]}, {"emoji": "🇬🇪", "aliases": ["georgia"]}, {"emoji": "🇬🇭", "aliases": ["ghana"]}, {"emoji": "👻", "aliases": ["ghost"]}, {"emoji": "🇬🇮", "aliases": ["gibraltar"]}, {"emoji": "🎁", "aliases": ["gift"]}, {"emoji": "💝", "aliases": ["gift_heart"]}, {"emoji": "🫚", "aliases": ["ginger_root"]}, {"emoji": "🦒", "aliases": ["giraffe"]}, {"emoji": "👧", "aliases": ["girl"]}, {"emoji": "🌐", "aliases": ["globe_with_meridians"]}, {"emoji": "🧤", "aliases": ["gloves"]}, {"emoji": "🥅", "aliases": ["goal_net"]}, {"emoji": "🐐", "aliases": ["goat"]}, {"emoji": "🥽", "aliases": ["goggles"]}, {"emoji": "⛳", "aliases": ["golf"]}, {"emoji": "🏌️", "aliases": ["golfing"]}, {"emoji": "🏌️‍♂️", "aliases": ["golfing_man"]}, {"emoji": "🏌️‍♀️", "aliases": ["golfing_woman"]}, {"emoji": "🪿", "aliases": ["goose"]}, {"emoji": "🦍", "aliases": ["gorilla"]}, {"emoji": "🍇", "aliases": ["grapes"]}, {"emoji": "🇬🇷", "aliases": ["greece"]}, {"emoji": "🍏", "aliases": ["green_apple"]}, {"emoji": "📗", "aliases": ["green_book"]}, {"emoji": "🟢", "aliases": ["green_circle"]}, {"emoji": "💚", "aliases": ["green_heart"]}, {"emoji": "🥗", "aliases": ["green_salad"]}, {"emoji": "🟩", "aliases": ["green_square"]}, {"emoji": "🇬🇱", "aliases": ["greenland"]}, {"emoji": "🇬🇩", "aliases": ["grenada"]}, {"emoji": "❕", "aliases": ["grey_exclamation"]}, {"emoji": "🩶", "aliases": ["grey_heart"]}, {"emoji": "❔", "aliases": ["grey_question"]}, {"emoji": "😬", "aliases": ["grimacing"]}, {"emoji": "😁", "aliases": ["grin"]}, {"emoji": "😀", "aliases": ["grinning"]}, {"emoji": "🇬🇵", "aliases": ["guadeloupe"]}, {"emoji": "🇬🇺", "aliases": ["guam"]}, {"emoji": "💂", "aliases": ["guard"]}, {"emoji": "💂‍♂️", "aliases": ["guardsman"]}, {"emoji": "💂‍♀️", "aliases": ["guardswoman"]}, {"emoji": "🇬🇹", "aliases": ["guatemala"]}, {"emoji": "🇬🇬", "aliases": ["guernsey"]}, {"emoji": "🦮", "aliases": ["guide_dog"]}, {"emoji": "🇬🇳", "aliases": ["guinea"]}, {"emoji": "🇬🇼", "aliases": ["guinea_bissau"]}, {"emoji": "🎸", "aliases": ["guitar"]}, {"emoji": "🔫", "aliases": ["gun"]}, {"emoji": "🇬🇾", "aliases": ["guyana"]}, {"emoji": "🪮", "aliases": ["hair_pick"]}, {"emoji": "💇", "aliases": ["haircut"]}, {"emoji": "💇‍♂️", "aliases": ["haircut_man"]}, {"emoji": "💇‍♀️", "aliases": ["haircut_woman"]}, {"emoji": "🇭🇹", "aliases": ["haiti"]}, {"emoji": "🍔", "aliases": ["hamburger"]}, {"emoji": "🔨", "aliases": ["hammer"]}, {"emoji": "⚒️", "aliases": ["hammer_and_pick"]}, {"emoji": "🛠️", "aliases": ["hammer_and_wrench"]}, {"emoji": "🪬", "aliases": ["hamsa"]}, {"emoji": "🐹", "aliases": ["hamster"]}, {"emoji": "✋", "aliases": ["hand", "raised_hand"]}, {"emoji": "🤭", "aliases": ["hand_over_mouth"]}, {"emoji": "🫰", "aliases": ["hand_with_index_finger_and_thumb_crossed"]}, {"emoji": "👜", "aliases": ["handbag"]}, {"emoji": "🤾", "aliases": ["handball_person"]}, {"emoji": "🤝", "aliases": ["handshake"]}, {"emoji": "💩", "aliases": ["hankey", "poop", "shit"]}, {"emoji": "#️⃣", "aliases": ["hash"]}, {"emoji": "🐥", "aliases": ["hatched_chick"]}, {"emoji": "🐣", "aliases": ["hatching_chick"]}, {"emoji": "🎧", "aliases": ["headphones"]}, {"emoji": "🪦", "aliases": ["headstone"]}, {"emoji": "🧑‍⚕️", "aliases": ["health_worker"]}, {"emoji": "🙉", "aliases": ["hear_no_evil"]}, {"emoji": "🇭🇲", "aliases": ["heard_mcdonald_islands"]}, {"emoji": "❤️", "aliases": ["heart"]}, {"emoji": "💟", "aliases": ["heart_decoration"]}, {"emoji": "😍", "aliases": ["heart_eyes"]}, {"emoji": "😻", "aliases": ["heart_eyes_cat"]}, {"emoji": "🫶", "aliases": ["heart_hands"]}, {"emoji": "❤️‍🔥", "aliases": ["heart_on_fire"]}, {"emoji": "💓", "aliases": ["heartbeat"]}, {"emoji": "💗", "aliases": ["heartpulse"]}, {"emoji": "♥️", "aliases": ["hearts"]}, {"emoji": "✔️", "aliases": ["heavy_check_mark"]}, {"emoji": "➗", "aliases": ["heavy_division_sign"]}, {"emoji": "💲", "aliases": ["heavy_dollar_sign"]}, {"emoji": "🟰", "aliases": ["heavy_equals_sign"]}, {"emoji": "❣️", "aliases": ["heavy_heart_exclamation"]}, {"emoji": "➖", "aliases": ["heavy_minus_sign"]}, {"emoji": "✖️", "aliases": ["heavy_multiplication_x"]}, {"emoji": "➕", "aliases": ["heavy_plus_sign"]}, {"emoji": "🦔", "aliases": ["hedgehog"]}, {"emoji": "🚁", "aliases": ["helicopter"]}, {"emoji": "🌿", "aliases": ["herb"]}, {"emoji": "🌺", "aliases": ["hibiscus"]}, {"emoji": "🔆", "aliases": ["high_brightness"]}, {"emoji": "👠", "aliases": ["high_heel"]}, {"emoji": "🥾", "aliases": ["hiking_boot"]}, {"emoji": "🛕", "aliases": ["hindu_temple"]}, {"emoji": "🦛", "aliases": ["hippopotamus"]}, {"emoji": "🔪", "aliases": ["hocho", "knife"]}, {"emoji": "🕳️", "aliases": ["hole"]}, {"emoji": "🇭🇳", "aliases": ["honduras"]}, {"emoji": "🍯", "aliases": ["honey_pot"]}, {"emoji": "🇭🇰", "aliases": ["hong_kong"]}, {"emoji": "🪝", "aliases": ["hook"]}, {"emoji": "🐴", "aliases": ["horse"]}, {"emoji": "🏇", "aliases": ["horse_racing"]}, {"emoji": "🏥", "aliases": ["hospital"]}, {"emoji": "🥵", "aliases": ["hot_face"]}, {"emoji": "🌶️", "aliases": ["hot_pepper"]}, {"emoji": "🌭", "aliases": ["hotdog"]}, {"emoji": "🏨", "aliases": ["hotel"]}, {"emoji": "♨️", "aliases": ["hotsprings"]}, {"emoji": "⌛", "aliases": ["hourglass"]}, {"emoji": "⏳", "aliases": ["hourglass_flowing_sand"]}, {"emoji": "🏠", "aliases": ["house"]}, {"emoji": "🏡", "aliases": ["house_with_garden"]}, {"emoji": "🏘️", "aliases": ["houses"]}, {"emoji": "🤗", "aliases": ["hugs"]}, {"emoji": "🇭🇺", "aliases": ["hungary"]}, {"emoji": "😯", "aliases": ["hushed"]}, {"emoji": "🛖", "aliases": ["hut"]}, {"emoji": "🪻", "aliases": ["hyacinth"]}, {"emoji": "🍨", "aliases": ["ice_cream"]}, {"emoji": "🧊", "aliases": ["ice_cube"]}, {"emoji": "🏒", "aliases": ["ice_hockey"]}, {"emoji": "⛸️", "aliases": ["ice_skate"]}, {"emoji": "🍦", "aliases": ["icecream"]}, {"emoji": "🇮🇸", "aliases": ["iceland"]}, {"emoji": "🆔", "aliases": ["id"]}, {"emoji": "🪪", "aliases": ["identification_card"]}, {"emoji": "🉐", "aliases": ["ideograph_advantage"]}, {"emoji": "👿", "aliases": ["imp"]}, {"emoji": "📥", "aliases": ["inbox_tray"]}, {"emoji": "📨", "aliases": ["incoming_envelope"]}, {"emoji": "🫵", "aliases": ["index_pointing_at_the_viewer"]}, {"emoji": "🇮🇳", "aliases": ["india"]}, {"emoji": "🇮🇩", "aliases": ["indonesia"]}, {"emoji": "♾️", "aliases": ["infinity"]}, {"emoji": "ℹ️", "aliases": ["information_source"]}, {"emoji": "😇", "aliases": ["innocent"]}, {"emoji": "⁉️", "aliases": ["interrobang"]}, {"emoji": "📱", "aliases": ["iphone"]}, {"emoji": "🇮🇷", "aliases": ["iran"]}, {"emoji": "🇮🇶", "aliases": ["iraq"]}, {"emoji": "🇮🇪", "aliases": ["ireland"]}, {"emoji": "🇮🇲", "aliases": ["isle_of_man"]}, {"emoji": "🇮🇱", "aliases": ["israel"]}, {"emoji": "🇮🇹", "aliases": ["it"]}, {"emoji": "🏮", "aliases": ["izakaya_lantern", "lantern"]}, {"emoji": "🎃", "aliases": ["jack_o_lantern"]}, {"emoji": "🇯🇲", "aliases": ["jamaica"]}, {"emoji": "🗾", "aliases": ["japan"]}, {"emoji": "🏯", "aliases": ["japanese_castle"]}, {"emoji": "👺", "aliases": ["japanese_goblin"]}, {"emoji": "👹", "aliases": ["japanese_ogre"]}, {"emoji": "🫙", "aliases": ["jar"]}, {"emoji": "👖", "aliases": ["jeans"]}, {"emoji": "🪼", "aliases": ["jellyfish"]}, {"emoji": "🇯🇪", "aliases": ["jersey"]}, {"emoji": "🧩", "aliases": ["jigsaw"]}, {"emoji": "🇯🇴", "aliases": ["jordan"]}, {"emoji": "😂", "aliases": ["joy"]}, {"emoji": "😹", "aliases": ["joy_cat"]}, {"emoji": "🕹️", "aliases": ["joystick"]}, {"emoji": "🇯🇵", "aliases": ["jp"]}, {"emoji": "🧑‍⚖️", "aliases": ["judge"]}, {"emoji": "🤹", "aliases": ["juggling_person"]}, {"emoji": "🕋", "aliases": ["kaaba"]}, {"emoji": "🦘", "aliases": ["kangaroo"]}, {"emoji": "🇰🇿", "aliases": ["kazakhstan"]}, {"emoji": "🇰🇪", "aliases": ["kenya"]}, {"emoji": "🔑", "aliases": ["key"]}, {"emoji": "⌨️", "aliases": ["keyboard"]}, {"emoji": "🔟", "aliases": ["keycap_ten"]}, {"emoji": "🪯", "aliases": ["khanda"]}, {"emoji": "🛴", "aliases": ["kick_scooter"]}, {"emoji": "👘", "aliases": ["kimono"]}, {"emoji": "🇰🇮", "aliases": ["kiribati"]}, {"emoji": "💋", "aliases": ["kiss"]}, {"emoji": "😗", "aliases": ["kissing"]}, {"emoji": "😽", "aliases": ["kissing_cat"]}, {"emoji": "😚", "aliases": ["kissing_closed_eyes"]}, {"emoji": "😘", "aliases": ["kissing_heart"]}, {"emoji": "😙", "aliases": ["kissing_smiling_eyes"]}, {"emoji": "🪁", "aliases": ["kite"]}, {"emoji": "🥝", "aliases": ["kiwi_fruit"]}, {"emoji": "🧎‍♂️", "aliases": ["kneeling_man"]}, {"emoji": "🧎", "aliases": ["kneeling_person"]}, {"emoji": "🧎‍♀️", "aliases": ["kneeling_woman"]}, {"emoji": "🪢", "aliases": ["knot"]}, {"emoji": "🐨", "aliases": ["koala"]}, {"emoji": "🈁", "aliases": ["koko"]}, {"emoji": "🇽🇰", "aliases": ["kosovo"]}, {"emoji": "🇰🇷", "aliases": ["kr"]}, {"emoji": "🇰🇼", "aliases": ["kuwait"]}, {"emoji": "🇰🇬", "aliases": ["kyrgyzstan"]}, {"emoji": "🥼", "aliases": ["lab_coat"]}, {"emoji": "🏷️", "aliases": ["label"]}, {"emoji": "🥍", "aliases": ["lacrosse"]}, {"emoji": "🪜", "aliases": ["ladder"]}, {"emoji": "🐞", "aliases": ["lady_beetle"]}, {"emoji": "🇱🇦", "aliases": ["laos"]}, {"emoji": "🔵", "aliases": ["large_blue_circle"]}, {"emoji": "🔷", "aliases": ["large_blue_diamond"]}, {"emoji": "🔶", "aliases": ["large_orange_diamond"]}, {"emoji": "🌗", "aliases": ["last_quarter_moon"]}, {"emoji": "🌜", "aliases": ["last_quarter_moon_with_face"]}, {"emoji": "✝️", "aliases": ["latin_cross"]}, {"emoji": "🇱🇻", "aliases": ["latvia"]}, {"emoji": "😆", "aliases": ["laughing", "satisfied", "laugh"]}, {"emoji": "🥬", "aliases": ["leafy_green"]}, {"emoji": "🍃", "aliases": ["leaves"]}, {"emoji": "🇱🇧", "aliases": ["lebanon"]}, {"emoji": "📒", "aliases": ["ledger"]}, {"emoji": "🛅", "aliases": ["left_luggage"]}, {"emoji": "↔️", "aliases": ["left_right_arrow"]}, {"emoji": "🗨️", "aliases": ["left_speech_bubble"]}, {"emoji": "↩️", "aliases": ["leftwards_arrow_with_hook"]}, {"emoji": "🫲", "aliases": ["leftwards_hand"]}, {"emoji": "🫷", "aliases": ["leftwards_pushing_hand"]}, {"emoji": "🦵", "aliases": ["leg"]}, {"emoji": "🍋", "aliases": ["lemon"]}, {"emoji": "♌", "aliases": ["leo"]}, {"emoji": "🐆", "aliases": ["leopard"]}, {"emoji": "🇱🇸", "aliases": ["lesotho"]}, {"emoji": "🎚️", "aliases": ["level_slider"]}, {"emoji": "🇱🇷", "aliases": ["liberia"]}, {"emoji": "♎", "aliases": ["libra"]}, {"emoji": "🇱🇾", "aliases": ["libya"]}, {"emoji": "🇱🇮", "aliases": ["liechtenstein"]}, {"emoji": "🩵", "aliases": ["light_blue_heart"]}, {"emoji": "🚈", "aliases": ["light_rail"]}, {"emoji": "🔗", "aliases": ["link"]}, {"emoji": "🦁", "aliases": ["lion"]}, {"emoji": "👄", "aliases": ["lips"]}, {"emoji": "💄", "aliases": ["lipstick"]}, {"emoji": "🇱🇹", "aliases": ["lithuania"]}, {"emoji": "🦎", "aliases": ["lizard"]}, {"emoji": "🦙", "aliases": ["llama"]}, {"emoji": "🦞", "aliases": ["lobster"]}, {"emoji": "🔒", "aliases": ["lock"]}, {"emoji": "🔏", "aliases": ["lock_with_ink_pen"]}, {"emoji": "🍭", "aliases": ["lollipop"]}, {"emoji": "🪘", "aliases": ["long_drum"]}, {"emoji": "➿", "aliases": ["loop"]}, {"emoji": "🧴", "aliases": ["lotion_bottle"]}, {"emoji": "🪷", "aliases": ["lotus"]}, {"emoji": "🧘", "aliases": ["lotus_position"]}, {"emoji": "🧘‍♂️", "aliases": ["lotus_position_man"]}, {"emoji": "🧘‍♀️", "aliases": ["lotus_position_woman"]}, {"emoji": "🔊", "aliases": ["loud_sound"]}, {"emoji": "📢", "aliases": ["loudspeaker"]}, {"emoji": "🏩", "aliases": ["love_hotel"]}, {"emoji": "💌", "aliases": ["love_letter"]}, {"emoji": "🤟", "aliases": ["love_you_gesture"]}, {"emoji": "🪫", "aliases": ["low_battery"]}, {"emoji": "🔅", "aliases": ["low_brightness"]}, {"emoji": "🧳", "aliases": ["luggage"]}, {"emoji": "🫁", "aliases": ["lungs"]}, {"emoji": "🇱🇺", "aliases": ["luxembourg"]}, {"emoji": "🤥", "aliases": ["lying_face"]}, {"emoji": "Ⓜ️", "aliases": ["m"]}, {"emoji": "🇲🇴", "aliases": ["macau"]}, {"emoji": "🇲🇰", "aliases": ["macedonia"]}, {"emoji": "🇲🇬", "aliases": ["madagascar"]}, {"emoji": "🔍", "aliases": ["mag"]}, {"emoji": "🔎", "aliases": ["mag_right"]}, {"emoji": "🧙", "aliases": ["mage"]}, {"emoji": "🧙‍♂️", "aliases": ["mage_man"]}, {"emoji": "🧙‍♀️", "aliases": ["mage_woman"]}, {"emoji": "🪄", "aliases": ["magic_wand"]}, {"emoji": "🧲", "aliases": ["magnet"]}, {"emoji": "🀄", "aliases": ["mahjong"]}, {"emoji": "📫", "aliases": ["mailbox"]}, {"emoji": "📪", "aliases": ["mailbox_closed"]}, {"emoji": "📬", "aliases": ["mailbox_with_mail"]}, {"emoji": "📭", "aliases": ["mailbox_with_no_mail"]}, {"emoji": "🇲🇼", "aliases": ["malawi"]}, {"emoji": "🇲🇾", "aliases": ["malaysia"]}, {"emoji": "🇲🇻", "aliases": ["maldives"]}, {"emoji": "🕵️‍♂️", "aliases": ["male_detective"]}, {"emoji": "♂️", "aliases": ["male_sign"]}, {"emoji": "🇲🇱", "aliases": ["mali"]}, {"emoji": "🇲🇹", "aliases": ["malta"]}, {"emoji": "🦣", "aliases": ["mammoth"]}, {"emoji": "👨", "aliases": ["man"]}, {"emoji": "👨‍🎨", "aliases": ["man_artist"]}, {"emoji": "👨‍🚀", "aliases": ["man_astronaut"]}, {"emoji": "🧔‍♂️", "aliases": ["man_beard"]}, {"emoji": "🤸‍♂️", "aliases": ["man_cartwheeling"]}, {"emoji": "👨‍🍳", "aliases": ["man_cook"]}, {"emoji": "🕺", "aliases": ["man_dancing"]}, {"emoji": "🤦‍♂️", "aliases": ["man_facepalming"]}, {"emoji": "👨‍🏭", "aliases": ["man_factory_worker"]}, {"emoji": "👨‍🌾", "aliases": ["man_farmer"]}, {"emoji": "👨‍🍼", "aliases": ["man_feeding_baby"]}, {"emoji": "👨‍🚒", "aliases": ["man_firefighter"]}, {"emoji": "👨‍⚕️", "aliases": ["man_health_worker"]}, {"emoji": "👨‍🦽", "aliases": ["man_in_manual_wheelchair"]}, {"emoji": "👨‍🦼", "aliases": ["man_in_motorized_wheelchair"]}, {"emoji": "🤵‍♂️", "aliases": ["man_in_tuxedo"]}, {"emoji": "👨‍⚖️", "aliases": ["man_judge"]}, {"emoji": "🤹‍♂️", "aliases": ["man_juggling"]}, {"emoji": "👨‍🔧", "aliases": ["man_mechanic"]}, {"emoji": "👨‍💼", "aliases": ["man_office_worker"]}, {"emoji": "👨‍✈️", "aliases": ["man_pilot"]}, {"emoji": "🤾‍♂️", "aliases": ["man_playing_handball"]}, {"emoji": "🤽‍♂️", "aliases": ["man_playing_water_polo"]}, {"emoji": "👨‍🔬", "aliases": ["man_scientist"]}, {"emoji": "🤷‍♂️", "aliases": ["man_shrugging"]}, {"emoji": "👨‍🎤", "aliases": ["man_singer"]}, {"emoji": "👨‍🎓", "aliases": ["man_student"]}, {"emoji": "👨‍🏫", "aliases": ["man_teacher"]}, {"emoji": "👨‍💻", "aliases": ["man_technologist"]}, {"emoji": "👲", "aliases": ["man_with_gua_pi_mao"]}, {"emoji": "👨‍🦯", "aliases": ["man_with_probing_cane"]}, {"emoji": "👳‍♂️", "aliases": ["man_with_turban"]}, {"emoji": "👰‍♂️", "aliases": ["man_with_veil"]}, {"emoji": "🥭", "aliases": ["mango"]}, {"emoji": "👞", "aliases": ["mans_shoe", "shoe"]}, {"emoji": "🕰️", "aliases": ["mantelpiece_clock"]}, {"emoji": "🦽", "aliases": ["manual_wheelchair"]}, {"emoji": "🍁", "aliases": ["maple_leaf"]}, {"emoji": "🪇", "aliases": ["maracas"]}, {"emoji": "🇲🇭", "aliases": ["marshall_islands"]}, {"emoji": "🥋", "aliases": ["martial_arts_uniform"]}, {"emoji": "🇲🇶", "aliases": ["martinique"]}, {"emoji": "😷", "aliases": ["mask"]}, {"emoji": "💆", "aliases": ["massage"]}, {"emoji": "💆‍♂️", "aliases": ["massage_man"]}, {"emoji": "💆‍♀️", "aliases": ["massage_woman"]}, {"emoji": "🧉", "aliases": ["mate"]}, {"emoji": "🇲🇷", "aliases": ["mauritania"]}, {"emoji": "🇲🇺", "aliases": ["mauritius"]}, {"emoji": "🇾🇹", "aliases": ["mayotte"]}, {"emoji": "🍖", "aliases": ["meat_on_bone"]}, {"emoji": "🧑‍🔧", "aliases": ["mechanic"]}, {"emoji": "🦾", "aliases": ["mechanical_arm"]}, {"emoji": "🦿", "aliases": ["mechanical_leg"]}, {"emoji": "🎖️", "aliases": ["medal_military"]}, {"emoji": "🏅", "aliases": ["medal_sports"]}, {"emoji": "⚕️", "aliases": ["medical_symbol"]}, {"emoji": "📣", "aliases": ["mega"]}, {"emoji": "🍈", "aliases": ["melon"]}, {"emoji": "🫠", "aliases": ["melting_face"]}, {"emoji": "📝", "aliases": ["memo", "pencil"]}, {"emoji": "🤼‍♂️", "aliases": ["men_wrestling"]}, {"emoji": "❤️‍🩹", "aliases": ["mending_heart"]}, {"emoji": "🕎", "aliases": ["menorah"]}, {"emoji": "🚹", "aliases": ["mens"]}, {"emoji": "🧜‍♀️", "aliases": ["mermaid"]}, {"emoji": "🧜‍♂️", "aliases": ["merman"]}, {"emoji": "🧜", "aliases": ["me<PERSON><PERSON>"]}, {"emoji": "🤘", "aliases": ["metal"]}, {"emoji": "🚇", "aliases": ["metro"]}, {"emoji": "🇲🇽", "aliases": ["mexico"]}, {"emoji": "🦠", "aliases": ["microbe"]}, {"emoji": "🇫🇲", "aliases": ["micronesia"]}, {"emoji": "🎤", "aliases": ["microphone"]}, {"emoji": "🔬", "aliases": ["microscope"]}, {"emoji": "🖕", "aliases": ["middle_finger", "fu"]}, {"emoji": "🪖", "aliases": ["military_helmet"]}, {"emoji": "🥛", "aliases": ["milk_glass"]}, {"emoji": "🌌", "aliases": ["milky_way"]}, {"emoji": "🚐", "aliases": ["minibus"]}, {"emoji": "💽", "aliases": ["minidisc"]}, {"emoji": "🪞", "aliases": ["mirror"]}, {"emoji": "🪩", "aliases": ["mirror_ball"]}, {"emoji": "📴", "aliases": ["mobile_phone_off"]}, {"emoji": "🇲🇩", "aliases": ["<PERSON><PERSON>"]}, {"emoji": "🇲🇨", "aliases": ["monaco"]}, {"emoji": "🤑", "aliases": ["money_mouth_face"]}, {"emoji": "💸", "aliases": ["money_with_wings"]}, {"emoji": "💰", "aliases": ["moneybag"]}, {"emoji": "🇲🇳", "aliases": ["mongolia"]}, {"emoji": "🐒", "aliases": ["monkey"]}, {"emoji": "🐵", "aliases": ["monkey_face"]}, {"emoji": "🧐", "aliases": ["monocle_face"]}, {"emoji": "🚝", "aliases": ["monorail"]}, {"emoji": "🇲🇪", "aliases": ["montenegro"]}, {"emoji": "🇲🇸", "aliases": ["montserrat"]}, {"emoji": "🌔", "aliases": ["moon", "waxing_gibbous_moon"]}, {"emoji": "🥮", "aliases": ["moon_cake"]}, {"emoji": "🫎", "aliases": ["moose"]}, {"emoji": "🇲🇦", "aliases": ["morocco"]}, {"emoji": "🎓", "aliases": ["mortar_board"]}, {"emoji": "🕌", "aliases": ["mosque"]}, {"emoji": "🦟", "aliases": ["mosquito"]}, {"emoji": "🛥️", "aliases": ["motor_boat"]}, {"emoji": "🛵", "aliases": ["motor_scooter"]}, {"emoji": "🏍️", "aliases": ["motorcycle"]}, {"emoji": "🦼", "aliases": ["motorized_wheelchair"]}, {"emoji": "🛣️", "aliases": ["motorway"]}, {"emoji": "🗻", "aliases": ["mount_fuji"]}, {"emoji": "⛰️", "aliases": ["mountain"]}, {"emoji": "🚵", "aliases": ["mountain_bicyclist"]}, {"emoji": "🚵‍♂️", "aliases": ["mountain_biking_man"]}, {"emoji": "🚵‍♀️", "aliases": ["mountain_biking_woman"]}, {"emoji": "🚠", "aliases": ["mountain_cableway"]}, {"emoji": "🚞", "aliases": ["mountain_railway"]}, {"emoji": "🏔️", "aliases": ["mountain_snow"]}, {"emoji": "🐭", "aliases": ["mouse"]}, {"emoji": "🐁", "aliases": ["mouse2"]}, {"emoji": "🪤", "aliases": ["mouse_trap"]}, {"emoji": "🎥", "aliases": ["movie_camera"]}, {"emoji": "🗿", "aliases": ["moyai"]}, {"emoji": "🇲🇿", "aliases": ["mozambique"]}, {"emoji": "🤶", "aliases": ["mrs_claus"]}, {"emoji": "💪", "aliases": ["muscle"]}, {"emoji": "🍄", "aliases": ["mushroom"]}, {"emoji": "🎹", "aliases": ["musical_keyboard"]}, {"emoji": "🎵", "aliases": ["musical_note"]}, {"emoji": "🎼", "aliases": ["musical_score"]}, {"emoji": "🔇", "aliases": ["mute"]}, {"emoji": "🧑‍🎄", "aliases": ["mx_claus"]}, {"emoji": "🇲🇲", "aliases": ["myanmar"]}, {"emoji": "💅", "aliases": ["nail_care"]}, {"emoji": "📛", "aliases": ["name_badge"]}, {"emoji": "🇳🇦", "aliases": ["namibia"]}, {"emoji": "🏞️", "aliases": ["national_park"]}, {"emoji": "🇳🇷", "aliases": ["nauru"]}, {"emoji": "🤢", "aliases": ["nauseated_face"]}, {"emoji": "🧿", "aliases": ["nazar_amulet"]}, {"emoji": "👔", "aliases": ["necktie"]}, {"emoji": "❎", "aliases": ["negative_squared_cross_mark"]}, {"emoji": "🇳🇵", "aliases": ["nepal"]}, {"emoji": "🤓", "aliases": ["nerd_face"]}, {"emoji": "🪺", "aliases": ["nest_with_eggs"]}, {"emoji": "🪆", "aliases": ["nesting_dolls"]}, {"emoji": "🇳🇱", "aliases": ["netherlands"]}, {"emoji": "😐", "aliases": ["neutral_face"]}, {"emoji": "🆕", "aliases": ["new"]}, {"emoji": "🇳🇨", "aliases": ["new_caledonia"]}, {"emoji": "🌑", "aliases": ["new_moon"]}, {"emoji": "🌚", "aliases": ["new_moon_with_face"]}, {"emoji": "🇳🇿", "aliases": ["new_zealand"]}, {"emoji": "📰", "aliases": ["newspaper"]}, {"emoji": "🗞️", "aliases": ["newspaper_roll"]}, {"emoji": "⏭️", "aliases": ["next_track_button"]}, {"emoji": "🆖", "aliases": ["ng"]}, {"emoji": "🇳🇮", "aliases": ["nicaragua"]}, {"emoji": "🇳🇪", "aliases": ["niger"]}, {"emoji": "🇳🇬", "aliases": ["nigeria"]}, {"emoji": "🌃", "aliases": ["night_with_stars"]}, {"emoji": "9️⃣", "aliases": ["nine"]}, {"emoji": "🥷", "aliases": ["ninja"]}, {"emoji": "🇳🇺", "aliases": ["niue"]}, {"emoji": "🔕", "aliases": ["no_bell"]}, {"emoji": "🚳", "aliases": ["no_bicycles"]}, {"emoji": "⛔", "aliases": ["no_entry"]}, {"emoji": "🚫", "aliases": ["no_entry_sign"]}, {"emoji": "🙅", "aliases": ["no_good"]}, {"emoji": "🙅‍♂️", "aliases": ["no_good_man", "ng_man"]}, {"emoji": "🙅‍♀️", "aliases": ["no_good_woman", "ng_woman"]}, {"emoji": "📵", "aliases": ["no_mobile_phones"]}, {"emoji": "😶", "aliases": ["no_mouth"]}, {"emoji": "🚷", "aliases": ["no_pedestrians"]}, {"emoji": "🚭", "aliases": ["no_smoking"]}, {"emoji": "🚱", "aliases": ["non-potable_water"]}, {"emoji": "🇳🇫", "aliases": ["norfolk_island"]}, {"emoji": "🇰🇵", "aliases": ["north_korea"]}, {"emoji": "🇲🇵", "aliases": ["northern_mariana_islands"]}, {"emoji": "🇳🇴", "aliases": ["norway"]}, {"emoji": "👃", "aliases": ["nose"]}, {"emoji": "📓", "aliases": ["notebook"]}, {"emoji": "📔", "aliases": ["notebook_with_decorative_cover"]}, {"emoji": "🎶", "aliases": ["notes"]}, {"emoji": "🔩", "aliases": ["nut_and_bolt"]}, {"emoji": "⭕", "aliases": ["o"]}, {"emoji": "🅾️", "aliases": ["o2"]}, {"emoji": "🌊", "aliases": ["ocean"]}, {"emoji": "🐙", "aliases": ["octopus"]}, {"emoji": "🍢", "aliases": ["oden"]}, {"emoji": "🏢", "aliases": ["office"]}, {"emoji": "🧑‍💼", "aliases": ["office_worker"]}, {"emoji": "🛢️", "aliases": ["oil_drum"]}, {"emoji": "🆗", "aliases": ["ok"]}, {"emoji": "👌", "aliases": ["ok_hand"]}, {"emoji": "🙆‍♂️", "aliases": ["ok_man"]}, {"emoji": "🙆", "aliases": ["ok_person"]}, {"emoji": "🙆‍♀️", "aliases": ["ok_woman"]}, {"emoji": "🗝️", "aliases": ["old_key"]}, {"emoji": "🧓", "aliases": ["older_adult"]}, {"emoji": "👴", "aliases": ["older_man"]}, {"emoji": "👵", "aliases": ["older_woman"]}, {"emoji": "🫒", "aliases": ["olive"]}, {"emoji": "🕉️", "aliases": ["om"]}, {"emoji": "🇴🇲", "aliases": ["oman"]}, {"emoji": "🔛", "aliases": ["on"]}, {"emoji": "🚘", "aliases": ["oncoming_automobile"]}, {"emoji": "🚍", "aliases": ["oncoming_bus"]}, {"emoji": "🚔", "aliases": ["oncoming_police_car"]}, {"emoji": "🚖", "aliases": ["oncoming_taxi"]}, {"emoji": "1️⃣", "aliases": ["one"]}, {"emoji": "🩱", "aliases": ["one_piece_swimsuit"]}, {"emoji": "🧅", "aliases": ["onion"]}, {"emoji": "📂", "aliases": ["open_file_folder"]}, {"emoji": "👐", "aliases": ["open_hands"]}, {"emoji": "😮", "aliases": ["open_mouth"]}, {"emoji": "☂️", "aliases": ["open_umbrella"]}, {"emoji": "⛎", "aliases": ["ophi<PERSON>us"]}, {"emoji": "📙", "aliases": ["orange_book"]}, {"emoji": "🟠", "aliases": ["orange_circle"]}, {"emoji": "🧡", "aliases": ["orange_heart"]}, {"emoji": "🟧", "aliases": ["orange_square"]}, {"emoji": "🦧", "aliases": ["orangutan"]}, {"emoji": "☦️", "aliases": ["orthodox_cross"]}, {"emoji": "🦦", "aliases": ["otter"]}, {"emoji": "📤", "aliases": ["outbox_tray"]}, {"emoji": "🦉", "aliases": ["owl"]}, {"emoji": "🐂", "aliases": ["ox"]}, {"emoji": "🦪", "aliases": ["oyster"]}, {"emoji": "📦", "aliases": ["package"]}, {"emoji": "📄", "aliases": ["page_facing_up"]}, {"emoji": "📃", "aliases": ["page_with_curl"]}, {"emoji": "📟", "aliases": ["pager"]}, {"emoji": "🖌️", "aliases": ["paintbrush"]}, {"emoji": "🇵🇰", "aliases": ["pakistan"]}, {"emoji": "🇵🇼", "aliases": ["palau"]}, {"emoji": "🇵🇸", "aliases": ["palestinian_territories"]}, {"emoji": "🫳", "aliases": ["palm_down_hand"]}, {"emoji": "🌴", "aliases": ["palm_tree"]}, {"emoji": "🫴", "aliases": ["palm_up_hand"]}, {"emoji": "🤲", "aliases": ["palms_up_together"]}, {"emoji": "🇵🇦", "aliases": ["panama"]}, {"emoji": "🥞", "aliases": ["pancakes"]}, {"emoji": "🐼", "aliases": ["panda_face"]}, {"emoji": "📎", "aliases": ["paperclip"]}, {"emoji": "🖇️", "aliases": ["paperclips"]}, {"emoji": "🇵🇬", "aliases": ["papua_new_guinea"]}, {"emoji": "🪂", "aliases": ["parachute"]}, {"emoji": "🇵🇾", "aliases": ["paraguay"]}, {"emoji": "⛱️", "aliases": ["parasol_on_ground"]}, {"emoji": "🅿️", "aliases": ["parking"]}, {"emoji": "🦜", "aliases": ["parrot"]}, {"emoji": "〽️", "aliases": ["part_alternation_mark"]}, {"emoji": "⛅", "aliases": ["partly_sunny"]}, {"emoji": "🥳", "aliases": ["partying_face"]}, {"emoji": "🛳️", "aliases": ["passenger_ship"]}, {"emoji": "🛂", "aliases": ["passport_control"]}, {"emoji": "⏸️", "aliases": ["pause_button"]}, {"emoji": "🫛", "aliases": ["pea_pod"]}, {"emoji": "☮️", "aliases": ["peace_symbol"]}, {"emoji": "🍑", "aliases": ["peach"]}, {"emoji": "🦚", "aliases": ["peacock"]}, {"emoji": "🥜", "aliases": ["peanuts"]}, {"emoji": "🍐", "aliases": ["pear"]}, {"emoji": "🖊️", "aliases": ["pen"]}, {"emoji": "✏️", "aliases": ["pencil2"]}, {"emoji": "🐧", "aliases": ["penguin"]}, {"emoji": "😔", "aliases": ["pensive"]}, {"emoji": "🧑‍🤝‍🧑", "aliases": ["people_holding_hands"]}, {"emoji": "🫂", "aliases": ["people_hugging"]}, {"emoji": "🎭", "aliases": ["performing_arts"]}, {"emoji": "😣", "aliases": ["persevere"]}, {"emoji": "🧑‍🦲", "aliases": ["person_bald"]}, {"emoji": "🧑‍🦱", "aliases": ["person_curly_hair"]}, {"emoji": "🧑‍🍼", "aliases": ["person_feeding_baby"]}, {"emoji": "🤺", "aliases": ["person_fencing"]}, {"emoji": "🧑‍🦽", "aliases": ["person_in_manual_wheelchair"]}, {"emoji": "🧑‍🦼", "aliases": ["person_in_motorized_wheelchair"]}, {"emoji": "🤵", "aliases": ["person_in_tuxedo"]}, {"emoji": "🧑‍🦰", "aliases": ["person_red_hair"]}, {"emoji": "🧑‍🦳", "aliases": ["person_white_hair"]}, {"emoji": "🫅", "aliases": ["person_with_crown"]}, {"emoji": "🧑‍🦯", "aliases": ["person_with_probing_cane"]}, {"emoji": "👳", "aliases": ["person_with_turban"]}, {"emoji": "👰", "aliases": ["person_with_veil"]}, {"emoji": "🇵🇪", "aliases": ["peru"]}, {"emoji": "🧫", "aliases": ["petri_dish"]}, {"emoji": "🇵🇭", "aliases": ["philippines"]}, {"emoji": "☎️", "aliases": ["phone", "telephone"]}, {"emoji": "⛏️", "aliases": ["pick"]}, {"emoji": "🛻", "aliases": ["pickup_truck"]}, {"emoji": "🥧", "aliases": ["pie"]}, {"emoji": "🐷", "aliases": ["pig"]}, {"emoji": "🐖", "aliases": ["pig2"]}, {"emoji": "🐽", "aliases": ["pig_nose"]}, {"emoji": "💊", "aliases": ["pill"]}, {"emoji": "🧑‍✈️", "aliases": ["pilot"]}, {"emoji": "🪅", "aliases": ["pinata"]}, {"emoji": "🤌", "aliases": ["pinched_fingers"]}, {"emoji": "🤏", "aliases": ["pinching_hand"]}, {"emoji": "🍍", "aliases": ["pineapple"]}, {"emoji": "🏓", "aliases": ["ping_pong"]}, {"emoji": "🩷", "aliases": ["pink_heart"]}, {"emoji": "🏴‍☠️", "aliases": ["pirate_flag"]}, {"emoji": "♓", "aliases": ["pisces"]}, {"emoji": "🇵🇳", "aliases": ["pitcairn_islands"]}, {"emoji": "🍕", "aliases": ["pizza"]}, {"emoji": "🪧", "aliases": ["placard"]}, {"emoji": "🛐", "aliases": ["place_of_worship"]}, {"emoji": "🍽️", "aliases": ["plate_with_cutlery"]}, {"emoji": "⏯️", "aliases": ["play_or_pause_button"]}, {"emoji": "🛝", "aliases": ["playground_slide"]}, {"emoji": "🥺", "aliases": ["pleading_face"]}, {"emoji": "🪠", "aliases": ["plunger"]}, {"emoji": "👇", "aliases": ["point_down"]}, {"emoji": "👈", "aliases": ["point_left"]}, {"emoji": "👉", "aliases": ["point_right"]}, {"emoji": "☝️", "aliases": ["point_up"]}, {"emoji": "👆", "aliases": ["point_up_2"]}, {"emoji": "🇵🇱", "aliases": ["poland"]}, {"emoji": "🐻‍❄️", "aliases": ["polar_bear"]}, {"emoji": "🚓", "aliases": ["police_car"]}, {"emoji": "👮", "aliases": ["police_officer", "cop"]}, {"emoji": "👮‍♂️", "aliases": ["policeman"]}, {"emoji": "👮‍♀️", "aliases": ["policewoman"]}, {"emoji": "🐩", "aliases": ["poodle"]}, {"emoji": "🍿", "aliases": ["popcorn"]}, {"emoji": "🇵🇹", "aliases": ["portugal"]}, {"emoji": "🏣", "aliases": ["post_office"]}, {"emoji": "📯", "aliases": ["postal_horn"]}, {"emoji": "📮", "aliases": ["postbox"]}, {"emoji": "🚰", "aliases": ["potable_water"]}, {"emoji": "🥔", "aliases": ["potato"]}, {"emoji": "🪴", "aliases": ["potted_plant"]}, {"emoji": "👝", "aliases": ["pouch"]}, {"emoji": "🍗", "aliases": ["poultry_leg"]}, {"emoji": "💷", "aliases": ["pound"]}, {"emoji": "🫗", "aliases": ["pouring_liquid"]}, {"emoji": "😾", "aliases": ["pouting_cat"]}, {"emoji": "🙎", "aliases": ["pouting_face"]}, {"emoji": "🙎‍♂️", "aliases": ["pouting_man"]}, {"emoji": "🙎‍♀️", "aliases": ["pouting_woman"]}, {"emoji": "🙏", "aliases": ["pray"]}, {"emoji": "📿", "aliases": ["prayer_beads"]}, {"emoji": "🫃", "aliases": ["pregnant_man"]}, {"emoji": "🫄", "aliases": ["pregnant_person"]}, {"emoji": "🤰", "aliases": ["pregnant_woman"]}, {"emoji": "🥨", "aliases": ["pretzel"]}, {"emoji": "⏮️", "aliases": ["previous_track_button"]}, {"emoji": "🤴", "aliases": ["prince"]}, {"emoji": "👸", "aliases": ["princess"]}, {"emoji": "🖨️", "aliases": ["printer"]}, {"emoji": "🦯", "aliases": ["probing_cane"]}, {"emoji": "🇵🇷", "aliases": ["puerto_rico"]}, {"emoji": "🟣", "aliases": ["purple_circle"]}, {"emoji": "💜", "aliases": ["purple_heart"]}, {"emoji": "🟪", "aliases": ["purple_square"]}, {"emoji": "👛", "aliases": ["purse"]}, {"emoji": "📌", "aliases": ["pushpin"]}, {"emoji": "🚮", "aliases": ["put_litter_in_its_place"]}, {"emoji": "🇶🇦", "aliases": ["qatar"]}, {"emoji": "❓", "aliases": ["question"]}, {"emoji": "🐰", "aliases": ["rabbit"]}, {"emoji": "🐇", "aliases": ["rabbit2"]}, {"emoji": "🦝", "aliases": ["raccoon"]}, {"emoji": "🐎", "aliases": ["racehorse"]}, {"emoji": "🏎️", "aliases": ["racing_car"]}, {"emoji": "📻", "aliases": ["radio"]}, {"emoji": "🔘", "aliases": ["radio_button"]}, {"emoji": "☢️", "aliases": ["radioactive"]}, {"emoji": "😡", "aliases": ["rage", "pout"]}, {"emoji": "🚃", "aliases": ["railway_car"]}, {"emoji": "🛤️", "aliases": ["railway_track"]}, {"emoji": "🌈", "aliases": ["rainbow"]}, {"emoji": "🏳️‍🌈", "aliases": ["rainbow_flag"]}, {"emoji": "🤚", "aliases": ["raised_back_of_hand"]}, {"emoji": "🤨", "aliases": ["raised_eyebrow"]}, {"emoji": "🖐️", "aliases": ["raised_hand_with_fingers_splayed"]}, {"emoji": "🙌", "aliases": ["raised_hands"]}, {"emoji": "🙋", "aliases": ["raising_hand"]}, {"emoji": "🙋‍♂️", "aliases": ["raising_hand_man"]}, {"emoji": "🙋‍♀️", "aliases": ["raising_hand_woman"]}, {"emoji": "🐏", "aliases": ["ram"]}, {"emoji": "🍜", "aliases": ["ramen"]}, {"emoji": "🐀", "aliases": ["rat"]}, {"emoji": "🪒", "aliases": ["razor"]}, {"emoji": "🧾", "aliases": ["receipt"]}, {"emoji": "⏺️", "aliases": ["record_button"]}, {"emoji": "♻️", "aliases": ["recycle"]}, {"emoji": "🔴", "aliases": ["red_circle"]}, {"emoji": "🧧", "aliases": ["red_envelope"]}, {"emoji": "👨‍🦰", "aliases": ["red_haired_man"]}, {"emoji": "👩‍🦰", "aliases": ["red_haired_woman"]}, {"emoji": "🟥", "aliases": ["red_square"]}, {"emoji": "®️", "aliases": ["registered"]}, {"emoji": "☺️", "aliases": ["relaxed"]}, {"emoji": "😌", "aliases": ["relieved"]}, {"emoji": "🎗️", "aliases": ["reminder_ribbon"]}, {"emoji": "🔁", "aliases": ["repeat"]}, {"emoji": "🔂", "aliases": ["repeat_one"]}, {"emoji": "⛑️", "aliases": ["rescue_worker_helmet"]}, {"emoji": "🚻", "aliases": ["restroom"]}, {"emoji": "🇷🇪", "aliases": ["reunion"]}, {"emoji": "💞", "aliases": ["revolving_hearts"]}, {"emoji": "⏪", "aliases": ["rewind"]}, {"emoji": "🦏", "aliases": ["rhinoceros"]}, {"emoji": "🎀", "aliases": ["ribbon"]}, {"emoji": "🍚", "aliases": ["rice"]}, {"emoji": "🍙", "aliases": ["rice_ball"]}, {"emoji": "🍘", "aliases": ["rice_cracker"]}, {"emoji": "🎑", "aliases": ["rice_scene"]}, {"emoji": "🗯️", "aliases": ["right_anger_bubble"]}, {"emoji": "🫱", "aliases": ["rightwards_hand"]}, {"emoji": "🫸", "aliases": ["rightwards_pushing_hand"]}, {"emoji": "💍", "aliases": ["ring"]}, {"emoji": "🛟", "aliases": ["ring_buoy"]}, {"emoji": "🪐", "aliases": ["ringed_planet"]}, {"emoji": "🤖", "aliases": ["robot"]}, {"emoji": "🪨", "aliases": ["rock"]}, {"emoji": "🚀", "aliases": ["rocket"]}, {"emoji": "🤣", "aliases": ["rofl"]}, {"emoji": "🙄", "aliases": ["roll_eyes"]}, {"emoji": "🧻", "aliases": ["roll_of_paper"]}, {"emoji": "🎢", "aliases": ["roller_coaster"]}, {"emoji": "🛼", "aliases": ["roller_skate"]}, {"emoji": "🇷🇴", "aliases": ["romania"]}, {"emoji": "🐓", "aliases": ["rooster"]}, {"emoji": "🌹", "aliases": ["rose"]}, {"emoji": "🏵️", "aliases": ["rosette"]}, {"emoji": "🚨", "aliases": ["rotating_light"]}, {"emoji": "📍", "aliases": ["round_pushpin"]}, {"emoji": "🚣", "aliases": ["rowboat"]}, {"emoji": "🚣‍♂️", "aliases": ["rowing_man"]}, {"emoji": "🚣‍♀️", "aliases": ["rowing_woman"]}, {"emoji": "🇷🇺", "aliases": ["ru"]}, {"emoji": "🏉", "aliases": ["rugby_football"]}, {"emoji": "🏃", "aliases": ["runner", "running"]}, {"emoji": "🏃‍♂️", "aliases": ["running_man"]}, {"emoji": "🎽", "aliases": ["running_shirt_with_sash"]}, {"emoji": "🏃‍♀️", "aliases": ["running_woman"]}, {"emoji": "🇷🇼", "aliases": ["rwanda"]}, {"emoji": "🈂️", "aliases": ["sa"]}, {"emoji": "🧷", "aliases": ["safety_pin"]}, {"emoji": "🦺", "aliases": ["safety_vest"]}, {"emoji": "♐", "aliases": ["sagittarius"]}, {"emoji": "🍶", "aliases": ["sake"]}, {"emoji": "🧂", "aliases": ["salt"]}, {"emoji": "🫡", "aliases": ["saluting_face"]}, {"emoji": "🇼🇸", "aliases": ["samoa"]}, {"emoji": "🇸🇲", "aliases": ["san_marino"]}, {"emoji": "👡", "aliases": ["sandal"]}, {"emoji": "🥪", "aliases": ["sandwich"]}, {"emoji": "🎅", "aliases": ["santa"]}, {"emoji": "🇸🇹", "aliases": ["sao_tome_principe"]}, {"emoji": "🥻", "aliases": ["sari"]}, {"emoji": "📡", "aliases": ["satellite"]}, {"emoji": "🇸🇦", "aliases": ["saudi_arabia"]}, {"emoji": "🧖‍♂️", "aliases": ["sauna_man"]}, {"emoji": "🧖", "aliases": ["sauna_person"]}, {"emoji": "🧖‍♀️", "aliases": ["sauna_woman"]}, {"emoji": "🦕", "aliases": ["sauropod"]}, {"emoji": "🎷", "aliases": ["saxophone"]}, {"emoji": "🧣", "aliases": ["scarf"]}, {"emoji": "🏫", "aliases": ["school"]}, {"emoji": "🎒", "aliases": ["school_satchel"]}, {"emoji": "🧑‍🔬", "aliases": ["scientist"]}, {"emoji": "✂️", "aliases": ["scissors"]}, {"emoji": "🦂", "aliases": ["scorpion"]}, {"emoji": "♏", "aliases": ["scorpius"]}, {"emoji": "🏴󠁧󠁢󠁳󠁣󠁴󠁿", "aliases": ["scotland"]}, {"emoji": "😱", "aliases": ["scream"]}, {"emoji": "🙀", "aliases": ["scream_cat"]}, {"emoji": "🪛", "aliases": ["screwdriver"]}, {"emoji": "📜", "aliases": ["scroll"]}, {"emoji": "🦭", "aliases": ["seal"]}, {"emoji": "💺", "aliases": ["seat"]}, {"emoji": "㊙️", "aliases": ["secret"]}, {"emoji": "🙈", "aliases": ["see_no_evil"]}, {"emoji": "🌱", "aliases": ["seedling"]}, {"emoji": "🤳", "aliases": ["selfie"]}, {"emoji": "🇸🇳", "aliases": ["senegal"]}, {"emoji": "🇷🇸", "aliases": ["serbia"]}, {"emoji": "🐕‍🦺", "aliases": ["service_dog"]}, {"emoji": "7️⃣", "aliases": ["seven"]}, {"emoji": "🪡", "aliases": ["sewing_needle"]}, {"emoji": "🇸🇨", "aliases": ["seychelles"]}, {"emoji": "🫨", "aliases": ["shaking_face"]}, {"emoji": "🥘", "aliases": ["shallow_pan_of_food"]}, {"emoji": "☘️", "aliases": ["shamrock"]}, {"emoji": "🦈", "aliases": ["shark"]}, {"emoji": "🍧", "aliases": ["shaved_ice"]}, {"emoji": "🐑", "aliases": ["sheep"]}, {"emoji": "🐚", "aliases": ["shell"]}, {"emoji": "🛡️", "aliases": ["shield"]}, {"emoji": "⛩️", "aliases": ["shinto_shrine"]}, {"emoji": "🚢", "aliases": ["ship"]}, {"emoji": "👕", "aliases": ["shirt", "tshirt"]}, {"emoji": "🛍️", "aliases": ["shopping"]}, {"emoji": "🛒", "aliases": ["shopping_cart"]}, {"emoji": "🩳", "aliases": ["shorts"]}, {"emoji": "🚿", "aliases": ["shower"]}, {"emoji": "🦐", "aliases": ["shrimp"]}, {"emoji": "🤷", "aliases": ["shrug"]}, {"emoji": "🤫", "aliases": ["shushing_face"]}, {"emoji": "🇸🇱", "aliases": ["sierra_leone"]}, {"emoji": "📶", "aliases": ["signal_strength"]}, {"emoji": "🇸🇬", "aliases": ["singapore"]}, {"emoji": "🧑‍🎤", "aliases": ["singer"]}, {"emoji": "🇸🇽", "aliases": ["sint_maarten"]}, {"emoji": "6️⃣", "aliases": ["six"]}, {"emoji": "🔯", "aliases": ["six_pointed_star"]}, {"emoji": "🛹", "aliases": ["skateboard"]}, {"emoji": "🎿", "aliases": ["ski"]}, {"emoji": "⛷️", "aliases": ["skier"]}, {"emoji": "💀", "aliases": ["skull"]}, {"emoji": "☠️", "aliases": ["skull_and_crossbones"]}, {"emoji": "🦨", "aliases": ["skunk"]}, {"emoji": "🛷", "aliases": ["sled"]}, {"emoji": "😴", "aliases": ["sleeping"]}, {"emoji": "🛌", "aliases": ["sleeping_bed"]}, {"emoji": "😪", "aliases": ["sleepy"]}, {"emoji": "🙁", "aliases": ["slightly_frowning_face"]}, {"emoji": "🙂", "aliases": ["slightly_smiling_face"]}, {"emoji": "🎰", "aliases": ["slot_machine"]}, {"emoji": "🦥", "aliases": ["sloth"]}, {"emoji": "🇸🇰", "aliases": ["slovakia"]}, {"emoji": "🇸🇮", "aliases": ["slovenia"]}, {"emoji": "🛩️", "aliases": ["small_airplane"]}, {"emoji": "🔹", "aliases": ["small_blue_diamond"]}, {"emoji": "🔸", "aliases": ["small_orange_diamond"]}, {"emoji": "🔺", "aliases": ["small_red_triangle"]}, {"emoji": "🔻", "aliases": ["small_red_triangle_down"]}, {"emoji": "😄", "aliases": ["smile"]}, {"emoji": "😸", "aliases": ["smile_cat"]}, {"emoji": "😃", "aliases": ["smiley"]}, {"emoji": "😺", "aliases": ["smiley_cat"]}, {"emoji": "🥲", "aliases": ["smiling_face_with_tear"]}, {"emoji": "🥰", "aliases": ["smiling_face_with_three_hearts"]}, {"emoji": "😈", "aliases": ["smiling_imp"]}, {"emoji": "😏", "aliases": ["smirk"]}, {"emoji": "😼", "aliases": ["smirk_cat"]}, {"emoji": "🚬", "aliases": ["smoking"]}, {"emoji": "🐌", "aliases": ["snail"]}, {"emoji": "🐍", "aliases": ["snake"]}, {"emoji": "🤧", "aliases": ["sneezing_face"]}, {"emoji": "🏂", "aliases": ["snowboarder"]}, {"emoji": "❄️", "aliases": ["snowflake"]}, {"emoji": "⛄", "aliases": ["snowman"]}, {"emoji": "☃️", "aliases": ["snowman_with_snow"]}, {"emoji": "🧼", "aliases": ["soap"]}, {"emoji": "😭", "aliases": ["sob"]}, {"emoji": "⚽", "aliases": ["soccer"]}, {"emoji": "🧦", "aliases": ["socks"]}, {"emoji": "🥎", "aliases": ["softball"]}, {"emoji": "🇸🇧", "aliases": ["solomon_islands"]}, {"emoji": "🇸🇴", "aliases": ["somalia"]}, {"emoji": "🔜", "aliases": ["soon"]}, {"emoji": "🆘", "aliases": ["sos"]}, {"emoji": "🔉", "aliases": ["sound"]}, {"emoji": "🇿🇦", "aliases": ["south_africa"]}, {"emoji": "🇬🇸", "aliases": ["south_georgia_south_sandwich_islands"]}, {"emoji": "🇸🇸", "aliases": ["south_sudan"]}, {"emoji": "👾", "aliases": ["space_invader"]}, {"emoji": "♠️", "aliases": ["spades"]}, {"emoji": "🍝", "aliases": ["spaghetti"]}, {"emoji": "❇️", "aliases": ["sparkle"]}, {"emoji": "🎇", "aliases": ["sparkler"]}, {"emoji": "✨", "aliases": ["sparkles"]}, {"emoji": "💖", "aliases": ["sparkling_heart"]}, {"emoji": "🙊", "aliases": ["speak_no_evil"]}, {"emoji": "🔈", "aliases": ["speaker"]}, {"emoji": "🗣️", "aliases": ["speaking_head"]}, {"emoji": "💬", "aliases": ["speech_balloon"]}, {"emoji": "🚤", "aliases": ["speedboat"]}, {"emoji": "🕷️", "aliases": ["spider"]}, {"emoji": "🕸️", "aliases": ["spider_web"]}, {"emoji": "🗓️", "aliases": ["spiral_calendar"]}, {"emoji": "🗒️", "aliases": ["spiral_notepad"]}, {"emoji": "🧽", "aliases": ["sponge"]}, {"emoji": "🥄", "aliases": ["spoon"]}, {"emoji": "🦑", "aliases": ["squid"]}, {"emoji": "🇱🇰", "aliases": ["sri_lanka"]}, {"emoji": "🇧🇱", "aliases": ["st_bar<PERSON><PERSON><PERSON>"]}, {"emoji": "🇸🇭", "aliases": ["st_helena"]}, {"emoji": "🇰🇳", "aliases": ["st_kitts_nevis"]}, {"emoji": "🇱🇨", "aliases": ["st_lucia"]}, {"emoji": "🇲🇫", "aliases": ["st_martin"]}, {"emoji": "🇵🇲", "aliases": ["st_pierre_miquelon"]}, {"emoji": "🇻🇨", "aliases": ["st_vincent_grenadines"]}, {"emoji": "🏟️", "aliases": ["stadium"]}, {"emoji": "🧍‍♂️", "aliases": ["standing_man"]}, {"emoji": "🧍", "aliases": ["standing_person"]}, {"emoji": "🧍‍♀️", "aliases": ["standing_woman"]}, {"emoji": "⭐", "aliases": ["star"]}, {"emoji": "🌟", "aliases": ["star2"]}, {"emoji": "☪️", "aliases": ["star_and_crescent"]}, {"emoji": "✡️", "aliases": ["star_of_david"]}, {"emoji": "🤩", "aliases": ["star_struck"]}, {"emoji": "🌠", "aliases": ["stars"]}, {"emoji": "🚉", "aliases": ["station"]}, {"emoji": "🗽", "aliases": ["statue_of_liberty"]}, {"emoji": "🚂", "aliases": ["steam_locomotive"]}, {"emoji": "🩺", "aliases": ["stethoscope"]}, {"emoji": "🍲", "aliases": ["stew"]}, {"emoji": "⏹️", "aliases": ["stop_button"]}, {"emoji": "🛑", "aliases": ["stop_sign"]}, {"emoji": "⏱️", "aliases": ["stopwatch"]}, {"emoji": "📏", "aliases": ["straight_ruler"]}, {"emoji": "🍓", "aliases": ["strawberry"]}, {"emoji": "😛", "aliases": ["stuck_out_tongue"]}, {"emoji": "😝", "aliases": ["stuck_out_tongue_closed_eyes"]}, {"emoji": "😜", "aliases": ["stuck_out_tongue_winking_eye"]}, {"emoji": "🧑‍🎓", "aliases": ["student"]}, {"emoji": "🎙️", "aliases": ["studio_microphone"]}, {"emoji": "🥙", "aliases": ["stuffed_flatbread"]}, {"emoji": "🇸🇩", "aliases": ["sudan"]}, {"emoji": "🌥️", "aliases": ["sun_behind_large_cloud"]}, {"emoji": "🌦️", "aliases": ["sun_behind_rain_cloud"]}, {"emoji": "🌤️", "aliases": ["sun_behind_small_cloud"]}, {"emoji": "🌞", "aliases": ["sun_with_face"]}, {"emoji": "🌻", "aliases": ["sunflower"]}, {"emoji": "😎", "aliases": ["sunglasses"]}, {"emoji": "☀️", "aliases": ["sunny"]}, {"emoji": "🌅", "aliases": ["sunrise"]}, {"emoji": "🌄", "aliases": ["sunrise_over_mountains"]}, {"emoji": "🦸", "aliases": ["superhero"]}, {"emoji": "🦸‍♂️", "aliases": ["superhero_man"]}, {"emoji": "🦸‍♀️", "aliases": ["superhero_woman"]}, {"emoji": "🦹", "aliases": ["supervillain"]}, {"emoji": "🦹‍♂️", "aliases": ["supervillain_man"]}, {"emoji": "🦹‍♀️", "aliases": ["supervillain_woman"]}, {"emoji": "🏄", "aliases": ["surfer"]}, {"emoji": "🏄‍♂️", "aliases": ["surfing_man"]}, {"emoji": "🏄‍♀️", "aliases": ["surfing_woman"]}, {"emoji": "🇸🇷", "aliases": ["suriname"]}, {"emoji": "🍣", "aliases": ["sushi"]}, {"emoji": "🚟", "aliases": ["suspension_railway"]}, {"emoji": "🇸🇯", "aliases": ["svalbard_jan_mayen"]}, {"emoji": "🦢", "aliases": ["swan"]}, {"emoji": "🇸🇿", "aliases": ["swaziland"]}, {"emoji": "😓", "aliases": ["sweat"]}, {"emoji": "💦", "aliases": ["sweat_drops"]}, {"emoji": "😅", "aliases": ["sweat_smile"]}, {"emoji": "🇸🇪", "aliases": ["sweden"]}, {"emoji": "🍠", "aliases": ["sweet_potato"]}, {"emoji": "🩲", "aliases": ["swim_brief"]}, {"emoji": "🏊", "aliases": ["swimmer"]}, {"emoji": "🏊‍♂️", "aliases": ["swimming_man"]}, {"emoji": "🏊‍♀️", "aliases": ["swimming_woman"]}, {"emoji": "🇨🇭", "aliases": ["switzerland"]}, {"emoji": "🔣", "aliases": ["symbols"]}, {"emoji": "🕍", "aliases": ["synagogue"]}, {"emoji": "🇸🇾", "aliases": ["syria"]}, {"emoji": "💉", "aliases": ["syringe"]}, {"emoji": "🦖", "aliases": ["t-rex"]}, {"emoji": "🌮", "aliases": ["taco"]}, {"emoji": "🎉", "aliases": ["tada", "hooray"]}, {"emoji": "🇹🇼", "aliases": ["taiwan"]}, {"emoji": "🇹🇯", "aliases": ["tajikistan"]}, {"emoji": "🥡", "aliases": ["takeout_box"]}, {"emoji": "🫔", "aliases": ["tamale"]}, {"emoji": "🎋", "aliases": ["tanabata_tree"]}, {"emoji": "🍊", "aliases": ["tangerine", "orange", "mandarin"]}, {"emoji": "🇹🇿", "aliases": ["tanzania"]}, {"emoji": "♉", "aliases": ["taurus"]}, {"emoji": "🚕", "aliases": ["taxi"]}, {"emoji": "🍵", "aliases": ["tea"]}, {"emoji": "🧑‍🏫", "aliases": ["teacher"]}, {"emoji": "🫖", "aliases": ["teapot"]}, {"emoji": "🧑‍💻", "aliases": ["technologist"]}, {"emoji": "🧸", "aliases": ["teddy_bear"]}, {"emoji": "📞", "aliases": ["telephone_receiver"]}, {"emoji": "🔭", "aliases": ["telescope"]}, {"emoji": "🎾", "aliases": ["tennis"]}, {"emoji": "⛺", "aliases": ["tent"]}, {"emoji": "🧪", "aliases": ["test_tube"]}, {"emoji": "🇹🇭", "aliases": ["thailand"]}, {"emoji": "🌡️", "aliases": ["thermometer"]}, {"emoji": "🤔", "aliases": ["thinking"]}, {"emoji": "🩴", "aliases": ["thong_sandal"]}, {"emoji": "💭", "aliases": ["thought_balloon"]}, {"emoji": "🧵", "aliases": ["thread"]}, {"emoji": "3️⃣", "aliases": ["three"]}, {"emoji": "🎫", "aliases": ["ticket"]}, {"emoji": "🎟️", "aliases": ["tickets"]}, {"emoji": "🐯", "aliases": ["tiger"]}, {"emoji": "🐅", "aliases": ["tiger2"]}, {"emoji": "⏲️", "aliases": ["timer_clock"]}, {"emoji": "🇹🇱", "aliases": ["timor_leste"]}, {"emoji": "💁‍♂️", "aliases": ["tipping_hand_man", "sassy_man"]}, {"emoji": "💁", "aliases": ["tipping_hand_person", "information_desk_person"]}, {"emoji": "💁‍♀️", "aliases": ["tipping_hand_woman", "sassy_woman"]}, {"emoji": "😫", "aliases": ["tired_face"]}, {"emoji": "™️", "aliases": ["tm"]}, {"emoji": "🇹🇬", "aliases": ["togo"]}, {"emoji": "🚽", "aliases": ["toilet"]}, {"emoji": "🇹🇰", "aliases": ["tokelau"]}, {"emoji": "🗼", "aliases": ["tokyo_tower"]}, {"emoji": "🍅", "aliases": ["tomato"]}, {"emoji": "🇹🇴", "aliases": ["tonga"]}, {"emoji": "👅", "aliases": ["tongue"]}, {"emoji": "🧰", "aliases": ["toolbox"]}, {"emoji": "🦷", "aliases": ["tooth"]}, {"emoji": "🪥", "aliases": ["toothbrush"]}, {"emoji": "🔝", "aliases": ["top"]}, {"emoji": "🎩", "aliases": ["tophat"]}, {"emoji": "🌪️", "aliases": ["tornado"]}, {"emoji": "🇹🇷", "aliases": ["tr"]}, {"emoji": "🖲️", "aliases": ["trackball"]}, {"emoji": "🚜", "aliases": ["tractor"]}, {"emoji": "🚥", "aliases": ["traffic_light"]}, {"emoji": "🚋", "aliases": ["train"]}, {"emoji": "🚆", "aliases": ["train2"]}, {"emoji": "🚊", "aliases": ["tram"]}, {"emoji": "🏳️‍⚧️", "aliases": ["transgender_flag"]}, {"emoji": "⚧️", "aliases": ["transgender_symbol"]}, {"emoji": "🚩", "aliases": ["triangular_flag_on_post"]}, {"emoji": "📐", "aliases": ["triangular_ruler"]}, {"emoji": "🔱", "aliases": ["trident"]}, {"emoji": "🇹🇹", "aliases": ["trinidad_tobago"]}, {"emoji": "🇹🇦", "aliases": ["tristan_<PERSON>_cunha"]}, {"emoji": "😤", "aliases": ["triumph"]}, {"emoji": "🧌", "aliases": ["troll"]}, {"emoji": "🚎", "aliases": ["trolleybus"]}, {"emoji": "🏆", "aliases": ["trophy"]}, {"emoji": "🍹", "aliases": ["tropical_drink"]}, {"emoji": "🐠", "aliases": ["tropical_fish"]}, {"emoji": "🚚", "aliases": ["truck"]}, {"emoji": "🎺", "aliases": ["trumpet"]}, {"emoji": "🌷", "aliases": ["tulip"]}, {"emoji": "🥃", "aliases": ["tumbler_glass"]}, {"emoji": "🇹🇳", "aliases": ["tunisia"]}, {"emoji": "🦃", "aliases": ["turkey"]}, {"emoji": "🇹🇲", "aliases": ["turkmenistan"]}, {"emoji": "🇹🇨", "aliases": ["turks_caicos_islands"]}, {"emoji": "🐢", "aliases": ["turtle"]}, {"emoji": "🇹🇻", "aliases": ["tuvalu"]}, {"emoji": "📺", "aliases": ["tv"]}, {"emoji": "🔀", "aliases": ["twisted_rightwards_arrows"]}, {"emoji": "2️⃣", "aliases": ["two"]}, {"emoji": "💕", "aliases": ["two_hearts"]}, {"emoji": "👬", "aliases": ["two_men_holding_hands"]}, {"emoji": "👭", "aliases": ["two_women_holding_hands"]}, {"emoji": "🈹", "aliases": ["u5272"]}, {"emoji": "🈴", "aliases": ["u5408"]}, {"emoji": "🈺", "aliases": ["u55b6"]}, {"emoji": "🈯", "aliases": ["u6307"]}, {"emoji": "🈷️", "aliases": ["u6708"]}, {"emoji": "🈶", "aliases": ["u6709"]}, {"emoji": "🈵", "aliases": ["u6e80"]}, {"emoji": "🈚", "aliases": ["u7121"]}, {"emoji": "🈸", "aliases": ["u7533"]}, {"emoji": "🈲", "aliases": ["u7981"]}, {"emoji": "🈳", "aliases": ["u7a7a"]}, {"emoji": "🇺🇬", "aliases": ["uganda"]}, {"emoji": "🇺🇦", "aliases": ["ukraine"]}, {"emoji": "☔", "aliases": ["umbrella"]}, {"emoji": "😒", "aliases": ["unamused"]}, {"emoji": "🔞", "aliases": ["underage"]}, {"emoji": "🦄", "aliases": ["unicorn"]}, {"emoji": "🇦🇪", "aliases": ["united_arab_emirates"]}, {"emoji": "🇺🇳", "aliases": ["united_nations"]}, {"emoji": "🔓", "aliases": ["unlock"]}, {"emoji": "🆙", "aliases": ["up"]}, {"emoji": "🙃", "aliases": ["upside_down_face"]}, {"emoji": "🇺🇾", "aliases": ["uruguay"]}, {"emoji": "🇺🇸", "aliases": ["us"]}, {"emoji": "🇺🇲", "aliases": ["us_outlying_islands"]}, {"emoji": "🇻🇮", "aliases": ["us_virgin_islands"]}, {"emoji": "🇺🇿", "aliases": ["uzbekistan"]}, {"emoji": "✌️", "aliases": ["v"]}, {"emoji": "🧛", "aliases": ["vampire"]}, {"emoji": "🧛‍♂️", "aliases": ["vampire_man"]}, {"emoji": "🧛‍♀️", "aliases": ["vampire_woman"]}, {"emoji": "🇻🇺", "aliases": ["vanuatu"]}, {"emoji": "🇻🇦", "aliases": ["vatican_city"]}, {"emoji": "🇻🇪", "aliases": ["venezuela"]}, {"emoji": "🚦", "aliases": ["vertical_traffic_light"]}, {"emoji": "📼", "aliases": ["vhs"]}, {"emoji": "📳", "aliases": ["vibration_mode"]}, {"emoji": "📹", "aliases": ["video_camera"]}, {"emoji": "🎮", "aliases": ["video_game"]}, {"emoji": "🇻🇳", "aliases": ["vietnam"]}, {"emoji": "🎻", "aliases": ["violin"]}, {"emoji": "♍", "aliases": ["virgo"]}, {"emoji": "🌋", "aliases": ["volcano"]}, {"emoji": "🏐", "aliases": ["volleyball"]}, {"emoji": "🤮", "aliases": ["vomiting_face"]}, {"emoji": "🆚", "aliases": ["vs"]}, {"emoji": "🖖", "aliases": ["vulcan_salute"]}, {"emoji": "🧇", "aliases": ["waffle"]}, {"emoji": "🏴󠁧󠁢󠁷󠁬󠁳󠁿", "aliases": ["wales"]}, {"emoji": "🚶", "aliases": ["walking"]}, {"emoji": "🚶‍♂️", "aliases": ["walking_man"]}, {"emoji": "🚶‍♀️", "aliases": ["walking_woman"]}, {"emoji": "🇼🇫", "aliases": ["wallis_futuna"]}, {"emoji": "🌘", "aliases": ["waning_crescent_moon"]}, {"emoji": "🌖", "aliases": ["waning_gibbous_moon"]}, {"emoji": "⚠️", "aliases": ["warning"]}, {"emoji": "🗑️", "aliases": ["wastebasket"]}, {"emoji": "⌚", "aliases": ["watch"]}, {"emoji": "🐃", "aliases": ["water_buffalo"]}, {"emoji": "🤽", "aliases": ["water_polo"]}, {"emoji": "🍉", "aliases": ["watermelon"]}, {"emoji": "👋", "aliases": ["wave"]}, {"emoji": "〰️", "aliases": ["wavy_dash"]}, {"emoji": "🌒", "aliases": ["waxing_crescent_moon"]}, {"emoji": "🚾", "aliases": ["wc"]}, {"emoji": "😩", "aliases": ["weary"]}, {"emoji": "💒", "aliases": ["wedding"]}, {"emoji": "🏋️", "aliases": ["weight_lifting"]}, {"emoji": "🏋️‍♂️", "aliases": ["weight_lifting_man"]}, {"emoji": "🏋️‍♀️", "aliases": ["weight_lifting_woman"]}, {"emoji": "🇪🇭", "aliases": ["western_sahara"]}, {"emoji": "🐳", "aliases": ["whale"]}, {"emoji": "🐋", "aliases": ["whale2"]}, {"emoji": "🛞", "aliases": ["wheel"]}, {"emoji": "☸️", "aliases": ["wheel_of_dharma"]}, {"emoji": "♿", "aliases": ["wheelchair"]}, {"emoji": "✅", "aliases": ["white_check_mark"]}, {"emoji": "⚪", "aliases": ["white_circle"]}, {"emoji": "🏳️", "aliases": ["white_flag"]}, {"emoji": "💮", "aliases": ["white_flower"]}, {"emoji": "👨‍🦳", "aliases": ["white_haired_man"]}, {"emoji": "👩‍🦳", "aliases": ["white_haired_woman"]}, {"emoji": "🤍", "aliases": ["white_heart"]}, {"emoji": "⬜", "aliases": ["white_large_square"]}, {"emoji": "◽", "aliases": ["white_medium_small_square"]}, {"emoji": "◻️", "aliases": ["white_medium_square"]}, {"emoji": "▫️", "aliases": ["white_small_square"]}, {"emoji": "🔳", "aliases": ["white_square_button"]}, {"emoji": "🥀", "aliases": ["wilted_flower"]}, {"emoji": "🎐", "aliases": ["wind_chime"]}, {"emoji": "🌬️", "aliases": ["wind_face"]}, {"emoji": "🪟", "aliases": ["window"]}, {"emoji": "🍷", "aliases": ["wine_glass"]}, {"emoji": "🪽", "aliases": ["wing"]}, {"emoji": "😉", "aliases": ["wink"]}, {"emoji": "🛜", "aliases": ["wireless"]}, {"emoji": "🐺", "aliases": ["wolf"]}, {"emoji": "👩", "aliases": ["woman"]}, {"emoji": "👩‍🎨", "aliases": ["woman_artist"]}, {"emoji": "👩‍🚀", "aliases": ["woman_astronaut"]}, {"emoji": "🧔‍♀️", "aliases": ["woman_beard"]}, {"emoji": "🤸‍♀️", "aliases": ["woman_cartwheeling"]}, {"emoji": "👩‍🍳", "aliases": ["woman_cook"]}, {"emoji": "💃", "aliases": ["woman_dancing", "dancer"]}, {"emoji": "🤦‍♀️", "aliases": ["woman_facepalming"]}, {"emoji": "👩‍🏭", "aliases": ["woman_factory_worker"]}, {"emoji": "👩‍🌾", "aliases": ["woman_farmer"]}, {"emoji": "👩‍🍼", "aliases": ["woman_feeding_baby"]}, {"emoji": "👩‍🚒", "aliases": ["woman_firefighter"]}, {"emoji": "👩‍⚕️", "aliases": ["woman_health_worker"]}, {"emoji": "👩‍🦽", "aliases": ["woman_in_manual_wheelchair"]}, {"emoji": "👩‍🦼", "aliases": ["woman_in_motorized_wheelchair"]}, {"emoji": "🤵‍♀️", "aliases": ["woman_in_tuxedo"]}, {"emoji": "👩‍⚖️", "aliases": ["woman_judge"]}, {"emoji": "🤹‍♀️", "aliases": ["woman_juggling"]}, {"emoji": "👩‍🔧", "aliases": ["woman_mechanic"]}, {"emoji": "👩‍💼", "aliases": ["woman_office_worker"]}, {"emoji": "👩‍✈️", "aliases": ["woman_pilot"]}, {"emoji": "🤾‍♀️", "aliases": ["woman_playing_handball"]}, {"emoji": "🤽‍♀️", "aliases": ["woman_playing_water_polo"]}, {"emoji": "👩‍🔬", "aliases": ["woman_scientist"]}, {"emoji": "🤷‍♀️", "aliases": ["woman_shrugging"]}, {"emoji": "👩‍🎤", "aliases": ["woman_singer"]}, {"emoji": "👩‍🎓", "aliases": ["woman_student"]}, {"emoji": "👩‍🏫", "aliases": ["woman_teacher"]}, {"emoji": "👩‍💻", "aliases": ["woman_technologist"]}, {"emoji": "🧕", "aliases": ["woman_with_headscarf"]}, {"emoji": "👩‍🦯", "aliases": ["woman_with_probing_cane"]}, {"emoji": "👳‍♀️", "aliases": ["woman_with_turban"]}, {"emoji": "👰‍♀️", "aliases": ["woman_with_veil", "bride_with_veil"]}, {"emoji": "👚", "aliases": ["womans_clothes"]}, {"emoji": "👒", "aliases": ["womans_hat"]}, {"emoji": "🤼‍♀️", "aliases": ["women_wrestling"]}, {"emoji": "🚺", "aliases": ["womens"]}, {"emoji": "🪵", "aliases": ["wood"]}, {"emoji": "🥴", "aliases": ["woozy_face"]}, {"emoji": "🗺️", "aliases": ["world_map"]}, {"emoji": "🪱", "aliases": ["worm"]}, {"emoji": "😟", "aliases": ["worried"]}, {"emoji": "🔧", "aliases": ["wrench"]}, {"emoji": "🤼", "aliases": ["wrestling"]}, {"emoji": "✍️", "aliases": ["writing_hand"]}, {"emoji": "❌", "aliases": ["x"]}, {"emoji": "🩻", "aliases": ["x_ray"]}, {"emoji": "🧶", "aliases": ["yarn"]}, {"emoji": "🥱", "aliases": ["yawning_face"]}, {"emoji": "🟡", "aliases": ["yellow_circle"]}, {"emoji": "💛", "aliases": ["yellow_heart"]}, {"emoji": "🟨", "aliases": ["yellow_square"]}, {"emoji": "🇾🇪", "aliases": ["yemen"]}, {"emoji": "💴", "aliases": ["yen"]}, {"emoji": "☯️", "aliases": ["yin_yang"]}, {"emoji": "🪀", "aliases": ["yo_yo"]}, {"emoji": "😋", "aliases": ["yum"]}, {"emoji": "🇿🇲", "aliases": ["zambia"]}, {"emoji": "🤪", "aliases": ["zany_face"]}, {"emoji": "⚡", "aliases": ["zap"]}, {"emoji": "🦓", "aliases": ["zebra"]}, {"emoji": "0️⃣", "aliases": ["zero"]}, {"emoji": "🇿🇼", "aliases": ["zimbabwe"]}, {"emoji": "🤐", "aliases": ["zipper_mouth_face"]}, {"emoji": "🧟", "aliases": ["zombie"]}, {"emoji": "🧟‍♂️", "aliases": ["zombie_man"]}, {"emoji": "🧟‍♀️", "aliases": ["zombie_woman"]}, {"emoji": "💤", "aliases": ["zzz"]}]