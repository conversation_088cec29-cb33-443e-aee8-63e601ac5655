name: files-changed

on:
  workflow_call:
    outputs:
      backend:
        value: ${{ jobs.detect.outputs.backend }}
      frontend:
        value: ${{ jobs.detect.outputs.frontend }}
      docs:
        value: ${{ jobs.detect.outputs.docs }}
      actions:
        value: ${{ jobs.detect.outputs.actions }}
      templates:
        value: ${{ jobs.detect.outputs.templates }}
      docker:
        value: ${{ jobs.detect.outputs.docker }}
      swagger:
        value: ${{ jobs.detect.outputs.swagger }}
      yaml:
        value: ${{ jobs.detect.outputs.yaml }}

jobs:
  detect:
    runs-on: ubuntu-latest
    timeout-minutes: 3
    outputs:
      backend: ${{ steps.changes.outputs.backend }}
      frontend: ${{ steps.changes.outputs.frontend }}
      docs: ${{ steps.changes.outputs.docs }}
      actions: ${{ steps.changes.outputs.actions }}
      templates: ${{ steps.changes.outputs.templates }}
      docker: ${{ steps.changes.outputs.docker }}
      swagger: ${{ steps.changes.outputs.swagger }}
      yaml: ${{ steps.changes.outputs.yaml }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            backend:
              - "**/*.go"
              - "templates/**/*.tmpl"
              - "assets/emoji.json"
              - "go.mod"
              - "go.sum"
              - "Makefile"
              - ".golangci.yml"
              - ".editorconfig"
              - "options/locale/locale_en-US.ini"

            frontend:
              - "*.js"
              - "*.ts"
              - "web_src/**"
              - "tools/*.js"
              - "tools/*.ts"
              - "assets/emoji.json"
              - "package.json"
              - "package-lock.json"
              - "Makefile"
              - ".eslintrc.cjs"
              - ".npmrc"

            docs:
              - "**/*.md"
              - ".markdownlint.yaml"
              - "package.json"
              - "package-lock.json"

            actions:
              - ".github/workflows/*"
              - "Makefile"

            templates:
              - "tools/lint-templates-*.js"
              - "templates/**/*.tmpl"
              - "pyproject.toml"
              - "uv.lock"

            docker:
              - "Dockerfile"
              - "Dockerfile.rootless"
              - "docker/**"
              - "Makefile"

            swagger:
              - "templates/swagger/v1_json.tmpl"
              - "templates/swagger/v1_input.json"
              - "Makefile"
              - "package.json"
              - "package-lock.json"
              - ".spectral.yaml"

            yaml:
              - "**/*.yml"
              - "**/*.yaml"
              - ".yamllint.yaml"
              - "pyproject.toml"
