{
  "name": "<PERSON><PERSON><PERSON>",
  "image": "mcr.microsoft.com/devcontainers/go:1.24-bookworm",
  "features": {
    // installs nodejs into container
    "ghcr.io/devcontainers/features/node:1": {
      "version": "lts"
    },
    "ghcr.io/devcontainers/features/git-lfs:1.2.2": {},
    "ghcr.io/jsburckhardt/devcontainer-features/uv:1": {},
    "ghcr.io/devcontainers/features/python:1": {
      "version": "3.12"
    },
    "ghcr.io/warrenbuckley/codespace-features/sqlite:1": {}
  },
  "customizations": {
    "vscode": {
      "settings": {},
      // same extensions as Gitpod, should match /.gitpod.yml
      "extensions": [
        "editorconfig.editorconfig",
        "dbaeumer.vscode-eslint",
        "golang.go",
        "stylelint.vscode-stylelint",
        "DavidAnson.vscode-markdownlint",
        "Vue.volar",
        "ms-azuretools.vscode-docker",
        "vitest.explorer",
        "cweijan.vscode-database-client2",
        "GitHub.vscode-pull-request-github",
        "Azurite.azurite"
      ]
    }
  },
  "portsAttributes": {
    "3000": {
      "label": "Gitea Web",
      "onAutoForward": "notify"
    }
  },
  "postCreateCommand": "make deps"
}
