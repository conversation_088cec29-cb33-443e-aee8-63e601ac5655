-
  id: 1
  repo_id: 1
  url: https://www.example.com/url1
  content_type: 1 # json
  events: '{"push_only":true,"send_everything":false,"choose_events":false,"events":{"create":false,"push":true,"pull_request":false}}'
  is_active: true

-
  id: 2
  repo_id: 1
  url: https://www.example.com/url2
  content_type: 1 # json
  events: '{"push_only":false,"send_everything":false,"choose_events":false,"events":{"create":false,"push":true,"pull_request":true}}'
  is_active: false

-
  id: 3
  owner_id: 3
  repo_id: 3
  url: https://www.example.com/url3
  content_type: 1 # json
  events: '{"push_only":false,"send_everything":false,"choose_events":false,"events":{"create":false,"push":true,"pull_request":true}}'
  is_active: true

-
  id: 4
  repo_id: 2
  url: https://www.example.com/url4
  content_type: 1 # json
  events: '{"push_only":true,"branch_filter":"{master,feature*}"}'
  is_active: true

-
  id: 5
  repo_id: 0
  owner_id: 0
  url: https://www.example.com/url5
  content_type: 1 # json
  events: '{"push_only":true,"branch_filter":"{master,feature*}"}'
  is_active: true
  is_system_webhook: true

-
  id: 6
  repo_id: 0
  owner_id: 0
  url: https://www.example.com/url6
  content_type: 1 # json
  events: '{"push_only":true,"branch_filter":"{master,feature*}"}'
  is_active: true
  is_system_webhook: false
