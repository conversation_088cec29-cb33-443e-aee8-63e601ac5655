-
  id: 1
  org_id: 3
  lower_name: owners
  name: Owners
  authorize: 4 # owner
  num_repos: 3
  num_members: 1
  includes_all_repositories: false
  can_create_org_repo: true

-
  id: 2
  org_id: 3
  lower_name: team1
  name: team1
  authorize: 2 # write
  num_repos: 1
  num_members: 2
  includes_all_repositories: false
  can_create_org_repo: false

-
  id: 3
  org_id: 6
  lower_name: owners
  name: Owners
  authorize: 4 # owner
  num_repos: 0
  num_members: 1
  includes_all_repositories: false
  can_create_org_repo: true

-
  id: 4
  org_id: 7
  lower_name: owners
  name: Owners
  authorize: 4 # owner
  num_repos: 0
  num_members: 1
  includes_all_repositories: false
  can_create_org_repo: true

-
  id: 5
  org_id: 17
  lower_name: owners
  name: Owners
  authorize: 4 # owner
  num_repos: 2
  num_members: 2
  includes_all_repositories: false
  can_create_org_repo: true

-
  id: 6
  org_id: 19
  lower_name: owners
  name: Owners
  authorize: 4 # owner
  num_repos: 2
  num_members: 2
  includes_all_repositories: false
  can_create_org_repo: true

-
  id: 7
  org_id: 3
  lower_name: test_team
  name: test_team
  authorize: 2 # write
  num_repos: 1
  num_members: 1
  includes_all_repositories: false
  can_create_org_repo: false

-
  id: 8
  org_id: 17
  lower_name: test_team
  name: test_team
  authorize: 2 # write
  num_repos: 1
  num_members: 1
  includes_all_repositories: false
  can_create_org_repo: false

-
  id: 9
  org_id: 17
  lower_name: review_team
  name: review_team
  authorize: 1 # read
  num_repos: 1
  num_members: 3
  includes_all_repositories: false
  can_create_org_repo: false

-
  id: 10
  org_id: 25
  lower_name: notowners
  name: NotOwners
  authorize: 1 # read
  num_repos: 0
  num_members: 1
  includes_all_repositories: false
  can_create_org_repo: false

-
  id: 11
  org_id: 26
  lower_name: team11
  name: team11
  authorize: 1 # read
  num_repos: 0
  num_members: 0
  includes_all_repositories: false
  can_create_org_repo: false

-
  id: 12
  org_id: 3
  lower_name: team12creators
  name: team12Creators
  authorize: 3 # admin
  num_repos: 0
  num_members: 1
  includes_all_repositories: false
  can_create_org_repo: true

-
  id: 13
  org_id: 6
  lower_name: team13notcreators
  name: team13NotCreators
  authorize: 3 # admin
  num_repos: 0
  num_members: 1
  includes_all_repositories: false
  can_create_org_repo: false

-
  id: 14
  org_id: 3
  lower_name: teamcreaterepo
  name: teamCreateRepo
  authorize: 2 # write
  num_repos: 0
  num_members: 1
  includes_all_repositories: false
  can_create_org_repo: true

-
  id: 15
  org_id: 22
  lower_name: owners
  name: Owners
  authorize: 4 # owner
  num_repos: 0
  num_members: 0
  includes_all_repositories: false
  can_create_org_repo: true

-
  id: 16
  org_id: 23
  lower_name: owners
  name: Owners
  authorize: 4 # owner
  num_repos: 0
  num_members: 0
  includes_all_repositories: false
  can_create_org_repo: true

-
  id: 17
  org_id: 23
  lower_name: team14writeauth
  name: team14WriteAuth
  authorize: 2 # write
  num_repos: 0
  num_members: 1
  includes_all_repositories: false
  can_create_org_repo: true

-
  id: 18
  org_id: 35
  lower_name: owners
  name: Owners
  authorize: 4 # owner
  num_repos: 0
  num_members: 1
  includes_all_repositories: false
  can_create_org_repo: true

-
  id: 19
  org_id: 36
  lower_name: owners
  name: Owners
  authorize: 4 # owner
  num_repos: 0
  num_members: 1
  includes_all_repositories: false
  can_create_org_repo: true

-
  id: 20
  org_id: 36
  lower_name: team20writepackage
  name: team20writepackage
  authorize: 1
  num_repos: 0
  num_members: 1
  includes_all_repositories: false
  can_create_org_repo: true

-
  id: 21
  org_id: 41
  lower_name: owners
  name: Owners
  authorize: 4 # owner
  num_repos: 1
  num_members: 1
  includes_all_repositories: true
  can_create_org_repo: true

-
  id: 22
  org_id: 41
  lower_name: team1
  name: Team1
  authorize: 1 # read
  num_repos: 1
  num_members: 2
  includes_all_repositories: false
  can_create_org_repo: false

-
  id: 23
  org_id: 25
  lower_name: owners
  name: Owners
  authorize: 4 # owner
  num_repos: 0
  num_members: 1
  includes_all_repositories: false
  can_create_org_repo: true

-
  id: 24
  org_id: 35
  lower_name: team24
  name: team24
  authorize: 2 # write
  num_repos: 0
  num_members: 1
  includes_all_repositories: true
  can_create_org_repo: false
