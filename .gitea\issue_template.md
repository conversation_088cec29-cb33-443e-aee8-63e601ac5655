<!-- NOTE: If your issue is a security concern, please send an <NAME_EMAIL> instead of opening a public issue -->

<!--
    1. Please speak English, this is the language all maintainers can speak and write.
    2. Please ask questions or configuration/deploy problems on our Discord
       server (https://discord.gg/gitea) or forum (https://forum.gitea.com).
    3. Please take a moment to check that your issue doesn't already exist.
    4. Make sure it's not mentioned in the FAQ (https://docs.gitea.com/help/faq)
    5. Please give all relevant information below for bug reports, because
       incomplete details will be handled as an invalid report.
-->

- Gitea version (or commit ref):
- Git version:
- Operating system:
  <!-- Please include information on whether you built gitea yourself, used one of our downloads or are using some other package -->
  <!-- Please also tell us how you are running gitea, e.g. if it is being run from docker, a command-line, systemd etc. --->
  <!-- If you are using a package or systemd tell us what distribution you are using -->
- Database (use `[x]`):
  - [ ] PostgreSQL
  - [ ] MySQL
  - [ ] MSSQL
  - [ ] SQLite
- Can you reproduce the bug at https://demo.gitea.com:
  - [ ] Yes (provide example URL)
  - [ ] No
- Log gist:
<!-- It really is important to provide pertinent logs -->
<!-- Please read https://docs.gitea.com/administration/logging-config#collecting-logs-for-help -->
<!-- In addition, if your problem relates to git commands set `RUN_MODE=dev` at the top of app.ini -->

## Description
<!-- If using a proxy or a CDN (e.g. CloudFlare) in front of gitea, please
     disable the proxy/CDN fully and connect to gitea directly to confirm
     the issue still persists without those services. -->

...


## Screenshots

<!-- **If this issue involves the Web Interface, please include a screenshot** -->
