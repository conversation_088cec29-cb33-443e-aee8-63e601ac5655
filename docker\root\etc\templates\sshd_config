Port ${SSH_LISTEN_PORT}
Protocol 2

AddressFamily any
ListenAddress 0.0.0.0
ListenAddress ::

${SSH_MAX_STARTUPS}
${SSH_MAX_SESSIONS}

LogLevel ${SSH_LOG_LEVEL}

HostKey /data/ssh/ssh_host_ed25519_key
${SSH_ED25519_CERT}
HostKey /data/ssh/ssh_host_rsa_key
${SSH_RSA_CERT}
HostKey /data/ssh/ssh_host_ecdsa_key
${SSH_ECDSA_CERT}

AuthorizedKeysFile .ssh/authorized_keys
AuthorizedPrincipalsFile .ssh/authorized_principals
TrustedUserCAKeys /data/git/.ssh/gitea-trusted-user-ca-keys.pem
CASignatureAlgorithms ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521,<EMAIL>,ssh-ed25519,<EMAIL>,rsa-sha2-512,rsa-sha2-256,ssh-rsa

UseDNS no
AllowAgentForwarding no
AllowTcpForwarding no
PrintMotd no

PermitUserEnvironment yes
PermitRootLogin no
ChallengeResponseAuthentication no
PasswordAuthentication no
PermitEmptyPasswords no

AllowUsers ${USER}

Banner none
Subsystem sftp /usr/lib/ssh/sftp-server

AcceptEnv GIT_PROTOCOL

${SSH_INCLUDE_FILE}
