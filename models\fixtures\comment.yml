-
  id: 1
  type: 7 # label
  poster_id: 2
  issue_id: 1 # in repo_id 1
  label_id: 1
  content: "1"
  created_unix: 946684810
-
  id: 2
  type: 0 # comment
  poster_id: 3 # user not watching (see watch.yml)
  issue_id: 1 # in repo_id 1
  content: "good work!"
  created_unix: 946684811
  updated_unix: 946684811
-
  id: 3
  type: 0 # comment
  poster_id: 5 # user not watching (see watch.yml)
  issue_id: 1 # in repo_id 1
  content: "meh..."
  created_unix: 946684812
  updated_unix: 946684812
-
  id: 4
  type: 21 # code comment
  poster_id: 1
  issue_id: 2
  content: "meh..."
  review_id: 4
  line: 4
  tree_path: "README.md"
  created_unix: 946684812
  invalidated: false
-
  id: 5
  type: 21 # code comment
  poster_id: 1
  issue_id: 2
  content: "meh..."
  line: -4
  tree_path: "README.md"
  created_unix: 946684812
  invalidated: false

-
  id: 6
  type: 21 # code comment
  poster_id: 1
  issue_id: 2
  content: "it's already invalidated. boring..."
  line: -4
  tree_path: "README.md"
  created_unix: 946684812
  invalidated: true

-
  id: 7
  type: 21 # code comment
  poster_id: 100
  issue_id: 3
  content: "a review from a deleted user"
  line: -4
  review_id: 10
  tree_path: "README.md"
  created_unix: 946684812
  invalidated: true

-
  id: 8
  type: 0 # comment
  poster_id: 2
  issue_id: 4 # in repo_id 2
  content: "comment in private pository"
  created_unix: 946684811
  updated_unix: 946684811

-
  id: 9
  type: 22 # review
  poster_id: 2
  issue_id: 2 # in repo_id 1
  review_id: 20
  created_unix: 946684810

-
  id: 10
  type: 22 # review
  poster_id: 5
  issue_id: 3 # in repo_id 1
  content: "reviewed by user5"
  review_id: 21
  created_unix: 946684816

-
  id: 11
  type: 27 # review request
  poster_id: 2
  issue_id: 3 # in repo_id 1
  content: "review request for user5"
  review_id: 22
  assignee_id: 5
  created_unix: 946684817
