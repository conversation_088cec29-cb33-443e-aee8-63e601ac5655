# NOTE: all users should have a password of "password"

- # NOTE: this user (id=1) is the admin
  id: 1
  lower_name: user1
  name: user1
  full_name: User One
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user1
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: true
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 2
  lower_name: user2
  name: user2
  full_name: '   < U<se>r Tw<o > ><  '
  email: <EMAIL>
  keep_email_private: true
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user2
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  # cause a random avatar to be generated when referenced for test purposes
  use_custom_avatar: false
  num_followers: 2
  num_following: 1
  num_stars: 2
  num_repos: 14
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 3
  lower_name: org3
  name: org3
  full_name: ' <<<< >> >> > >> > >>> >> '
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: onmention
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: org3
  type: 1
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: false
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 3
  num_teams: 5
  num_members: 3
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 4
  lower_name: user4
  name: user4
  full_name: '          '
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: onmention
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user4
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 1
  num_stars: 0
  num_repos: 0
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 5
  lower_name: user5
  name: user5
  full_name: User Five
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user5
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: false
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 1
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 6
  lower_name: org6
  name: org6
  full_name: Org Six
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: org6
  type: 1
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: false
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 2
  num_members: 2
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 7
  lower_name: org7
  name: org7
  full_name: Org Seven
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: disabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: org7
  type: 1
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: false
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 1
  num_members: 1
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 8
  lower_name: user8
  name: user8
  full_name: User Eight
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user8
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 1
  num_following: 1
  num_stars: 0
  num_repos: 0
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 9
  lower_name: user9
  name: user9
  full_name: User Nine
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: onmention
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user9
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: false
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false
  created_unix: 1730468968

-
  id: 10
  lower_name: user10
  name: user10
  full_name: User Ten
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user10
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 2
  num_repos: 3
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 11
  lower_name: user11
  name: user11
  full_name: User Eleven
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user11
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 1
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 12
  lower_name: user12
  name: user12
  full_name: User 12
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user12
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 1
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 13
  lower_name: user13
  name: user13
  full_name: User 13
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user13
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 1
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 14
  lower_name: user14
  name: user14
  full_name: User 14
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user14
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 3
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 15
  lower_name: user15
  name: user15
  full_name: User 15
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user15
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 4
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 16
  lower_name: user16
  name: user16
  full_name: User 16
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user16
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 2
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 17
  lower_name: org17
  name: org17
  full_name: org 17
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: org17
  type: 1
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 2
  num_teams: 3
  num_members: 5
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 18
  lower_name: user18
  name: user18
  full_name: User 18
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user18
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 19
  lower_name: org19
  name: org19
  full_name: Org 19
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: org19
  type: 1
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 2
  num_teams: 1
  num_members: 2
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 20
  lower_name: user20
  name: user20
  full_name: User 20
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user20
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 4
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 21
  lower_name: user21
  name: user21
  full_name: User 21
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user21
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 2
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 22
  lower_name: limited_org
  name: limited_org
  full_name: Limited Org
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: limited_org
  type: 1
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 2
  num_teams: 1
  num_members: 0
  visibility: 1
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 23
  lower_name: privated_org
  name: privated_org
  full_name: Privated Org
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: privated_org
  type: 1
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 2
  num_teams: 2
  num_members: 1
  visibility: 2
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 24
  lower_name: user24
  name: user24
  full_name: user24
  email: <EMAIL>
  keep_email_private: true
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user24
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 25
  lower_name: org25
  name: org25
  full_name: org25
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: org25
  type: 1
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: false
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 2
  num_members: 2
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 26
  lower_name: org26
  name: org26
  full_name: Org26
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: onmention
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: org26
  type: 1
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: false
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 4
  num_teams: 1
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: true
  theme: ""
  keep_activity_private: false

-
  id: 27
  lower_name: user27
  name: user27
  full_name: User Twenty-Seven
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user27
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 3
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 28
  lower_name: user28
  name: user28
  full_name: user27
  email: <EMAIL>
  keep_email_private: true
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user28
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 29
  lower_name: user29
  name: user29
  full_name: User 29
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user29
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: true
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 30
  lower_name: user30
  name: user30
  full_name: User Thirty
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user30
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 4
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 31
  lower_name: user31
  name: user31
  full_name: user31
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user31
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 1
  num_stars: 0
  num_repos: 0
  num_teams: 0
  num_members: 0
  visibility: 2
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 32
  lower_name: user32
  name: user32
  full_name: User 32 (U2F test)
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:notpassword
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user32
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 33
  lower_name: user33
  name: user33
  full_name: User 33 (Limited Visibility)
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user33
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 1
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 0
  num_members: 0
  visibility: 1
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 34
  lower_name: the_34-user.with.all.allowedchars
  name: the_34-user.with.all.allowedChars
  full_name: the_1-user.with.all.allowedChars
  description: 'some [commonmark](https://commonmark.org/)!'
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: the_34-user.with.all.allowedchars
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: false
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 35
  lower_name: private_org35
  name: private_org35
  full_name: Private Org 35
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: private_org35
  type: 1
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 2
  num_members: 2
  visibility: 2
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 36
  lower_name: limited_org36
  name: limited_org36
  full_name: Limited Org 36
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: limited_org36
  type: 1
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 2
  num_members: 2
  visibility: 1
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 37
  lower_name: user37
  name: user37
  full_name: User 37
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user37
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: true
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 38
  lower_name: user38
  name: user38
  full_name: User38
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user38
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 39
  lower_name: user39
  name: user39
  full_name: User39
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: enabled
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user39
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 0
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 40
  lower_name: user40
  name: user40
  full_name: User40
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: onmention
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: user40
  type: 0
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: true
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 1
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 41
  lower_name: org41
  name: org41
  full_name: Org41
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: onmention
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: org41
  type: 1
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: false
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 1
  num_teams: 2
  num_members: 3
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false

-
  id: 42
  lower_name: org42
  name: org42
  full_name: Org42
  email: <EMAIL>
  keep_email_private: false
  email_notifications_preference: onmention
  passwd: ZogKvWdyEx:password
  passwd_hash_algo: dummy
  must_change_password: false
  login_source: 0
  login_name: org42
  type: 1
  salt: ZogKvWdyEx
  max_repo_creation: -1
  is_active: false
  is_admin: false
  is_restricted: false
  allow_git_hook: false
  allow_import_local: false
  allow_create_organization: true
  prohibit_login: false
  avatar: ""
  avatar_email: <EMAIL>
  use_custom_avatar: true
  num_followers: 0
  num_following: 0
  num_stars: 0
  num_repos: 1
  num_teams: 0
  num_members: 0
  visibility: 0
  repo_admin_change_team_access: false
  theme: ""
  keep_activity_private: false
