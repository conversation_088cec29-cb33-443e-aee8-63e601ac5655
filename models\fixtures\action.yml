-
  id: 1
  user_id: 2
  op_type: 12 # close issue
  act_user_id: 2
  repo_id: 2 # private
  is_private: true
  created_unix: 1603228283

-
  id: 2
  user_id: 3
  op_type: 2 # rename repo
  act_user_id: 2
  repo_id: 3 # private
  is_private: true
  content: oldRepoName

-
  id: 3
  user_id: 11
  op_type: 1 # create repo
  act_user_id: 11
  repo_id: 9 # public
  is_private: false

-
  id: 4
  user_id: 16
  op_type: 12 # close issue
  act_user_id: 16
  repo_id: 22 # private
  is_private: true
  created_unix: 1603267920

- id: 5
  user_id: 10
  op_type: 1 # create repo
  act_user_id: 10
  repo_id: 6 # private
  is_private: true
  created_unix: 1603010100

- id: 6
  user_id: 10
  op_type: 1 # create repo
  act_user_id: 10
  repo_id: 7 # private
  is_private: true
  created_unix: 1603011300

- id: 7
  user_id: 10
  op_type: 1 # create repo
  act_user_id: 10
  repo_id: 8 # public
  is_private: false
  created_unix: 1603011540 # grouped with id:7

- id: 8
  user_id: 1
  op_type: 12 # close issue
  act_user_id: 1
  repo_id: 1700 # dangling intentional
  is_private: false
  created_unix: 1603011541

- id: 9
  user_id: 34
  op_type: 12 # close issue
  act_user_id: 34
  repo_id: 1 # public
  is_private: false
  created_unix: 1680454039
  content: '4|' # issueId 5
