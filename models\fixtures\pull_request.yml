-
  id: 1
  type: 0 # gitea pull request
  status: 2 # mergeable
  issue_id: 2
  index: 2
  head_repo_id: 1
  base_repo_id: 1
  head_branch: branch1
  base_branch: master
  merge_base: 4a357436d925b5c974181ff12a994538ddc5a269
  merged_commit_id: 1a8823cd1a9549fde083f992f6b9b87a7ab74fb3
  has_merged: true
  merger_id: 2

-
  id: 2
  type: 0 # gitea pull request
  status: 2 # mergeable
  issue_id: 3
  index: 3
  head_repo_id: 1
  base_repo_id: 1
  head_branch: branch2
  base_branch: master
  merge_base: 4a357436d925b5c974181ff12a994538ddc5a269
  has_merged: false

-
  id: 3
  type: 0 # gitea pull request
  status: 2 # mergeable
  issue_id: 8
  index: 1
  head_repo_id: 11
  base_repo_id: 10
  head_branch: branch2
  base_branch: master
  merge_base: 0abcb056019adb83
  has_merged: false

-
  id: 4
  type: 0 # gitea pull request
  status: 2 # mergeable
  issue_id: 9
  index: 1
  head_repo_id: 48
  base_repo_id: 48
  head_branch: branch1
  base_branch: master
  merge_base: abcdef1234567890
  has_merged: false

-
  id: 5 # this PR is outdated (one commit behind branch1 )
  type: 0 # gitea pull request
  status: 2 # mergeable
  issue_id: 11
  index: 5
  head_repo_id: 1
  base_repo_id: 1
  head_branch: pr-to-update
  base_branch: branch2
  merge_base: 985f0301dba5e7b34be866819cd15ad3d8f508ee
  has_merged: false

-
  id: 6
  type: 0 # gitea pull request
  status: 2 # mergeable
  issue_id: 12
  index: 2
  head_repo_id: 3
  base_repo_id: 3
  head_branch: test_branch
  base_branch: master
  merge_base: 2a47ca4b614a9f5a
  has_merged: false

-
  id: 7
  type: 0 # gitea pull request
  status: 2 # mergeable
  issue_id: 19
  index: 1
  head_repo_id: 58
  base_repo_id: 58
  head_branch: branch1
  base_branch: main
  merge_base: cbff181af4c9c7fee3cf6c106699e07d9a3f54e6
  has_merged: false

-
  id: 8
  type: 0 # gitea pull request
  status: 2 # mergeable
  issue_id: 20
  index: 1
  head_repo_id: 23
  base_repo_id: 23

-
  id: 9
  type: 0 # gitea pull request
  status: 2 # mergeable
  issue_id: 21
  index: 1
  head_repo_id: 60
  base_repo_id: 60

-
  id: 10
  type: 0 # gitea pull request
  status: 2 # mergeable
  issue_id: 22
  index: 1
  head_repo_id: 61
  base_repo_id: 61
